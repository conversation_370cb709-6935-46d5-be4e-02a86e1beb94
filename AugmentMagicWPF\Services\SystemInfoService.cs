using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Principal;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public class SystemInfoService : ISystemInfoService
    {
        public SystemInfo GetSystemInfo()
        {
            return new SystemInfo
            {
                SystemName = GetSystemName(),
                Architecture = GetArchitecture(),
                Version = GetVersion(),
                IsAdmin = IsAdministrator(),
                MachineName = Environment.MachineName,
                UserName = Environment.UserName,
                TotalMemory = GetTotalMemory(),
                AvailableMemory = GetAvailableMemory(),
                ProcessorName = GetProcessorName(),
                ProcessorCores = Environment.ProcessorCount,
                DotNetVersion = GetDotNetVersion()
            };
        }

        public bool IsAdministrator()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var identity = WindowsIdentity.GetCurrent();
                    var principal = new WindowsPrincipal(identity);
                    return principal.IsInRole(WindowsBuiltInRole.Administrator);
                }
                else
                {
                    // For Linux/macOS, check if running as root
                    return Environment.UserName == "root";
                }
            }
            catch
            {
                return false;
            }
        }

        public string GetExecutableFilename()
        {
            var system = GetSystemName().ToLower();
            var arch = GetArchitecture().ToLower();
            var filename = $"augment-magic-{system}-{arch}";
            
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                filename += ".exe";
            }
            
            return filename;
        }

        public string GetResourcePath(string relativePath)
        {
            try
            {
                // Try to get the application directory
                var appDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                if (!string.IsNullOrEmpty(appDir))
                {
                    var fullPath = Path.Combine(appDir, relativePath);
                    if (File.Exists(fullPath) || Directory.Exists(fullPath))
                    {
                        return fullPath;
                    }
                }

                // Fallback to current directory
                var currentDir = Environment.CurrentDirectory;
                return Path.Combine(currentDir, relativePath);
            }
            catch
            {
                return relativePath;
            }
        }

        private string GetSystemName()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return "Windows";
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                return "macOS";
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                return "Linux";
            else
                return "Unknown";
        }

        private string GetArchitecture()
        {
            var arch = RuntimeInformation.ProcessArchitecture;
            return arch switch
            {
                Architecture.X64 => "x86_64",
                Architecture.Arm64 => "aarch64",
                Architecture.X86 => "x86",
                Architecture.Arm => "arm",
                _ => arch.ToString()
            };
        }

        private string GetVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.0.0.0";
            }
            catch
            {
                return "1.0.0.0";
            }
        }

        private long GetTotalMemory()
        {
            try
            {
                var gc = GC.GetTotalMemory(false);
                return gc;
            }
            catch
            {
                return 0;
            }
        }

        private long GetAvailableMemory()
        {
            try
            {
                // This is a simplified implementation
                // In a real application, you might want to use WMI or other APIs
                return GetTotalMemory() / 2; // Rough estimate
            }
            catch
            {
                return 0;
            }
        }

        private string GetProcessorName()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // On Windows, you could use WMI to get processor name
                    return "Intel/AMD Processor";
                }
                else
                {
                    return "Unknown Processor";
                }
            }
            catch
            {
                return "Unknown Processor";
            }
        }

        private string GetDotNetVersion()
        {
            try
            {
                return RuntimeInformation.FrameworkDescription;
            }
            catch
            {
                return ".NET Unknown";
            }
        }
    }
}
