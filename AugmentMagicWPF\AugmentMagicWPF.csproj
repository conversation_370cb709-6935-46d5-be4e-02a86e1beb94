<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <!-- <ApplicationIcon>Resources\app_icon.ico</ApplicationIcon> -->
    <AssemblyTitle>Augment Magic</AssemblyTitle>
    <AssemblyDescription>Augment Magic - Advanced System Tool</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>Augment Code</Company>
    <Product>Augment Magic</Product>
    <Copyright>Copyright © 2024 Augment Code</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\Icons\" />
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Data\" />
  </ItemGroup>

</Project>
