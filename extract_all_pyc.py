#!/usr/bin/env python3
"""
提取所有 .pyc 文件中的字符串和信息
"""

import os
import re
from pathlib import Path

def find_strings(data, min_length=3):
    """在二进制数据中查找可打印字符串"""
    strings = []
    current_string = ""
    
    for byte in data:
        if 32 <= byte <= 126:  # 可打印ASCII字符
            current_string += chr(byte)
        else:
            if len(current_string) >= min_length:
                strings.append(current_string)
            current_string = ""
    
    if len(current_string) >= min_length:
        strings.append(current_string)
    
    return strings

def extract_pyc_info(file_path):
    """提取 .pyc 文件信息"""
    with open(file_path, 'rb') as f:
        data = f.read()
    
    strings = find_strings(data, min_length=3)
    
    # 过滤有用的字符串
    useful_strings = []
    for s in strings:
        # 过滤掉太短或无意义的字符串
        if len(s) >= 3 and not s.isspace():
            # 保留包含字母的字符串
            if any(c.isalpha() for c in s):
                useful_strings.append(s)
    
    return useful_strings

def create_module_from_strings(module_name, strings):
    """基于字符串创建模块文件"""
    lines = [f'"""', f'{module_name} - 从 .pyc 文件重构', f'"""', '']
    
    # 查找可能的导入
    imports = []
    for s in strings:
        if any(keyword in s.lower() for keyword in ['import', 'from']):
            imports.append(s)
    
    if imports:
        lines.append('# 可能的导入:')
        for imp in imports:
            lines.append(f'# {imp}')
        lines.append('')
    
    # 查找可能的类名和函数名
    classes = []
    functions = []
    
    for s in strings:
        # 查找类名（通常以大写字母开头）
        if s[0].isupper() and s.isalnum() and len(s) > 3:
            if s not in ['True', 'False', 'None'] and not s.isdigit():
                classes.append(s)
        
        # 查找函数名（包含下划线或以小写字母开头）
        if ('_' in s or s[0].islower()) and s.replace('_', '').isalnum():
            if len(s) > 3 and s not in ['self', 'args', 'kwargs']:
                functions.append(s)
    
    if classes:
        lines.append('# 可能的类:')
        for cls in set(classes):
            lines.append(f'class {cls}:')
            lines.append('    """重构的类"""')
            lines.append('    pass')
            lines.append('')
    
    if functions:
        lines.append('# 可能的函数:')
        for func in set(functions):
            if func.endswith('r') and len(func) > 1:
                func = func[:-1]  # 移除末尾的 'r'
            lines.append(f'def {func}():')
            lines.append('    """重构的函数"""')
            lines.append('    pass')
            lines.append('')
    
    # 添加所有找到的字符串作为注释
    lines.append('# 从 .pyc 文件中提取的所有字符串:')
    for i, s in enumerate(strings):
        if len(s) <= 100:  # 只显示较短的字符串
            lines.append(f'# {i+1:3d}: {repr(s)}')
    
    return '\n'.join(lines)

def main():
    """主函数"""
    extracted_dir = Path("AugmentMagic.exe_extracted")
    output_dir = Path("reconstructed_modules")
    output_dir.mkdir(exist_ok=True)
    
    # 查找所有 .pyc 文件
    pyc_files = list(extracted_dir.glob("*.pyc"))
    
    print(f"找到 {len(pyc_files)} 个 .pyc 文件:")
    for pyc_file in pyc_files:
        print(f"  - {pyc_file.name}")
    
    print("\n开始提取...")
    
    for pyc_file in pyc_files:
        print(f"\n处理: {pyc_file.name}")
        
        try:
            strings = extract_pyc_info(pyc_file)
            print(f"  提取到 {len(strings)} 个字符串")
            
            # 创建重构的模块
            module_name = pyc_file.stem
            module_content = create_module_from_strings(module_name, strings)
            
            # 保存到文件
            output_file = output_dir / f"{module_name}_reconstructed.py"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(module_content)
            
            print(f"  保存到: {output_file}")
            
            # 显示前10个有用的字符串
            useful_strings = [s for s in strings if len(s) >= 5 and any(c.isalpha() for c in s)][:10]
            if useful_strings:
                print("  主要字符串:")
                for s in useful_strings:
                    print(f"    - {repr(s)}")
        
        except Exception as e:
            print(f"  错误: {e}")
    
    print(f"\n完成！重构的文件保存在 {output_dir} 目录中。")

if __name__ == "__main__":
    main()
