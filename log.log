2025-07-28 11:29:34,302 [+] pydumpck is a multi-threads tool for decompile exe,elf,pyz,pyc packed by python which is base on pycdc and uncompyle6.sometimes its py-file result not exactly right ,maybe could use uncompyle6.
--------------------
pydumpck initilizing with 1.17.9 :pyinstaller_dump.py:115-20  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\pyinstaller_dump.py on(pyinstaller_dump.run) at 29040@MainThread  
2025-07-28 11:29:34,303 [*] plugins loaded with ['pycdc'] :__init__.py:155-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.load_plugins) at 29040@MainThread  
2025-07-28 11:29:34,304 [*] target file input:AugmentMagic.exe
to:decompiled_output :__init__.py:190-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.main) at 29040@MainThread  
2025-07-28 11:29:34,305 [*] start dump target file. type:arch :__init__.py:203-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.main) at 29040@MainThread  
2025-07-28 11:29:37,310 [+] struct file found:b'' :__init__.py:136-20  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.add) at 29040@MainThread  
2025-07-28 11:29:37,311 [*] 
export pyc :__init__.py:94-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 29040@MainThread  
2025-07-28 11:29:40,471 [*] 
decompile pyc :__init__.py:96-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 29040@MainThread  
2025-07-28 11:29:40,478 [!] Exception on decompile bytecode file:decompiled_output\pyi_rth_multiprocessing.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_9  
2025-07-28 11:29:40,480 [!] Exception on decompile bytecode file:decompiled_output\pyimod02_importers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_2  
2025-07-28 11:29:40,481 [!] Exception on decompile bytecode file:decompiled_output\pyimod03_ctypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_3  
2025-07-28 11:29:40,485 [!] Exception on decompile bytecode file:decompiled_output\pyimod01_archive.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_1  
2025-07-28 11:29:40,486 [!] Exception on decompile bytecode file:decompiled_output\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_11  
2025-07-28 11:29:40,487 [!] Exception on decompile bytecode file:decompiled_output\pyi_rth_setuptools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_7  
2025-07-28 11:29:40,488 [!] Exception on decompile bytecode file:decompiled_output\pyi_rth_pkgutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_8  
2025-07-28 11:29:40,490 [!] Exception on decompile bytecode file:decompiled_output\struct.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_0  
2025-07-28 11:29:40,490 [!] Exception on decompile bytecode file:decompiled_output\pyi_rth_inspect.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_6  
2025-07-28 11:29:40,491 [!] Exception on decompile bytecode file:decompiled_output\pyi_rth__tkinter.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_10  
2025-07-28 11:29:40,492 [!] Exception on decompile bytecode file:decompiled_output\pyiboot01_bootstrap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_5  
2025-07-28 11:29:40,493 [!] Exception on decompile bytecode file:decompiled_output\pyimod04_pywin32.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_4  
2025-07-28 11:29:41,476 [*] 
extract pyz :__init__.py:98-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 29040@MainThread  
2025-07-28 11:29:41,477 [-] Warning: This script is running in a different Python version than the one used to build the executable. :pyz_extract.py:48-40  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\pyc_checker\pyz_extract.py on(pyz_extract._handle_file) at 29040@MainThread  
2025-07-28 11:29:41,478 [+] Found 536 files in PYZ archive :pyz_extract.py:60-20  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\pyc_checker\pyz_extract.py on(pyz_extract._handle_file) at 29040@MainThread  
2025-07-28 11:29:42,713 [*] 
decompile pyc for `extract pyz` :__init__.py:100-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 29040@MainThread  
2025-07-28 11:29:42,725 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_47  
2025-07-28 11:29:42,781 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\base_futures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_41  
2025-07-28 11:29:42,791 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\streams.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_60  
2025-07-28 11:29:42,794 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_strptime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_35  
2025-07-28 11:29:42,799 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\_parseaddr.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_118  
2025-07-28 11:29:42,803 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_threading_local.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_36  
2025-07-28 11:29:42,805 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\threads.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_64  
2025-07-28 11:29:42,806 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\_minimal_curses.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_16  
2025-07-28 11:29:42,807 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_sitebuiltins.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_34  
2025-07-28 11:29:42,808 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\bz2.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_74  
2025-07-28 11:29:42,808 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_compression.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_13  
2025-07-28 11:29:42,809 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\curses\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_108  
2025-07-28 11:29:42,810 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\futures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_49  
2025-07-28 11:29:42,811 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_39  
2025-07-28 11:29:42,811 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_85  
2025-07-28 11:29:42,814 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\_functional.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_184  
2025-07-28 11:29:42,816 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_78  
2025-07-28 11:29:42,817 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pydecimal.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_0  
2025-07-28 11:29:42,818 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ast.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_38  
2025-07-28 11:29:42,818 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\http\cookies.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_151  
2025-07-28 11:29:42,820 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\concurrent\futures\_base.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_90  
2025-07-28 11:29:42,822 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\unix_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_68  
2025-07-28 11:29:42,823 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email_client.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_135  
2025-07-28 11:29:42,823 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_99  
2025-07-28 11:29:42,824 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\windows_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_69  
2025-07-28 11:29:42,825 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\macholib\dylib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_104  
2025-07-28 11:29:42,827 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\_header_value_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_117  
2025-07-28 11:29:42,827 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\base64mime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_120  
2025-07-28 11:29:42,829 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\queues.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_55  
2025-07-28 11:29:42,830 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\gettext.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_141  
2025-07-28 11:29:42,831 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\macholib\framework.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_105  
2025-07-28 11:29:42,932 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\idna\idnadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_155  
2025-07-28 11:29:42,934 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\subprocess.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_61  
2025-07-28 11:29:42,935 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\message.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_130  
2025-07-28 11:29:42,936 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\fnmatch.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_136  
2025-07-28 11:29:42,938 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\gzip.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_143  
2025-07-28 11:29:42,939 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\http\client.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_149  
2025-07-28 11:29:42,941 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\argparse.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_37  
2025-07-28 11:29:42,942 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_51  
2025-07-28 11:29:42,944 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\header.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_127  
2025-07-28 11:29:42,945 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\_functional.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_176  
2025-07-28 11:29:42,947 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\fractions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_137  
2025-07-28 11:29:42,948 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\getpass.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_140  
2025-07-28 11:29:42,952 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_compat_pickle.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_3  
2025-07-28 11:29:42,954 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\readers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_172  
2025-07-28 11:29:42,955 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_131  
2025-07-28 11:29:42,956 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\csv.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_98  
2025-07-28 11:29:42,956 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\transports.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_66  
2025-07-28 11:29:42,957 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\runners.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_56  
2025-07-28 11:29:42,959 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\base_tasks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_43  
2025-07-28 11:29:42,960 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\coroutines.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_45  
2025-07-28 11:29:42,960 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\http\cookiejar.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_150  
2025-07-28 11:29:42,962 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\code.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_86  
2025-07-28 11:29:42,962 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\codeop.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_87  
2025-07-28 11:29:42,963 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_124  
2025-07-28 11:29:42,963 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\concurrent\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_88  
2025-07-28 11:29:42,965 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\config.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_93  
2025-07-28 11:29:42,966 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\taskgroups.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_62  
2025-07-28 11:29:42,967 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\keymap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_22  
2025-07-28 11:29:42,969 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_aix_support.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_2  
2025-07-28 11:29:42,969 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\_encoded_words.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_116  
2025-07-28 11:29:42,970 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\proactor_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_53  
2025-07-28 11:29:42,971 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\iterators.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_129  
2025-07-28 11:29:42,972 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\future\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_189  
2025-07-28 11:29:42,973 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\macholib\dyld.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_103  
2025-07-28 11:29:42,976 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\machinery.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_164  
2025-07-28 11:29:42,977 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\staggered.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_59  
2025-07-28 11:29:42,978 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\protocols.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_54  
2025-07-28 11:29:42,978 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_180  
2025-07-28 11:29:42,980 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pydatetime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_8  
2025-07-28 11:29:42,980 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\locks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_50  
2025-07-28 11:29:42,982 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\encoders.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_123  
2025-07-28 11:29:42,984 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\policy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_132  
2025-07-28 11:29:42,984 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\quoprimime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_133  
2025-07-28 11:29:42,985 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_142  
2025-07-28 11:29:42,986 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\decimal.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_112  
2025-07-28 11:29:42,987 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\difflib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_113  
2025-07-28 11:29:42,987 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\http\server.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_152  
2025-07-28 11:29:42,988 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_115  
2025-07-28 11:29:42,989 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\idna\intranges.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_156  
2025-07-28 11:29:42,989 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\__future__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_9  
2025-07-28 11:29:42,990 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_159  
2025-07-28 11:29:42,990 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\_policybase.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_119  
2025-07-28 11:29:42,991 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\concurrent\futures\process.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_91  
2025-07-28 11:29:42,992 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\generator.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_126  
2025-07-28 11:29:42,993 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\configparser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_94  
2025-07-28 11:29:42,994 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_181  
2025-07-28 11:29:42,996 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\tasks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_63  
2025-07-28 11:29:43,009 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\html\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_146  
2025-07-28 11:29:43,051 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\certifi\core.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_77  
2025-07-28 11:29:43,126 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\_bootstrap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_161  
2025-07-28 11:29:43,169 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\html\entities.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_147  
2025-07-28 11:29:43,170 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\copy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_97  
2025-07-28 11:29:43,172 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_46  
2025-07-28 11:29:43,172 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\_threading_handler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_10  
2025-07-28 11:29:43,173 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\selector_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_57  
2025-07-28 11:29:43,174 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\concurrent\futures\thread.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_92  
2025-07-28 11:29:43,175 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\headerregistry.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_128  
2025-07-28 11:29:43,176 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\pager.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_24  
2025-07-28 11:29:43,178 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\concurrent\futures\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_89  
2025-07-28 11:29:43,178 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\feedparser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_125  
2025-07-28 11:29:43,179 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_173  
2025-07-28 11:29:43,181 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\windows_utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_70  
2025-07-28 11:29:43,182 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\hashlib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_144  
2025-07-28 11:29:43,183 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_106  
2025-07-28 11:29:43,184 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\dis.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_114  
2025-07-28 11:29:43,184 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\format_helpers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_48  
2025-07-28 11:29:43,184 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\_abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_160  
2025-07-28 11:29:43,185 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\models.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_83  
2025-07-28 11:29:43,186 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\contextlib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_95  
2025-07-28 11:29:43,187 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\_meta.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_170  
2025-07-28 11:29:43,187 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\timeouts.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_65  
2025-07-28 11:29:43,189 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\_aix.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_100  
2025-07-28 11:29:43,190 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\unix_console.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_30  
2025-07-28 11:29:43,190 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\base64.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_72  
2025-07-28 11:29:43,191 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\calendar.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_75  
2025-07-28 11:29:43,191 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\_bootstrap_external.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_162  
2025-07-28 11:29:43,193 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_163  
2025-07-28 11:29:43,194 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\contentmanager.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_122  
2025-07-28 11:29:43,195 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\inspect.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_47  
2025-07-28 11:29:43,196 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_177  
2025-07-28 11:29:43,196 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_23  
2025-07-28 11:29:43,197 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\macholib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_102  
2025-07-28 11:29:43,199 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\readline.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_26  
2025-07-28 11:29:43,200 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\idna\package_data.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_157  
2025-07-28 11:29:43,200 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\base_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_40  
2025-07-28 11:29:43,202 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\mixins.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_52  
2025-07-28 11:29:43,202 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_84  
2025-07-28 11:29:43,203 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\input.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_21  
2025-07-28 11:29:43,204 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\getopt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_139  
2025-07-28 11:29:43,204 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\hmac.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_145  
2025-07-28 11:29:43,206 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\api.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_79  
2025-07-28 11:29:43,208 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\simple_interact.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_27  
2025-07-28 11:29:43,209 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_165  
2025-07-28 11:29:43,210 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_134  
2025-07-28 11:29:43,210 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_182  
2025-07-28 11:29:43,211 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ftplib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_138  
2025-07-28 11:29:43,213 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\fancy_termios.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_19  
2025-07-28 11:29:43,213 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_6  
2025-07-28 11:29:43,214 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\trsock.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_67  
2025-07-28 11:29:43,214 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_colorize.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_12  
2025-07-28 11:29:43,215 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\trace.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_28  
2025-07-28 11:29:43,216 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\wintypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_107  
2025-07-28 11:29:43,216 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\dataclasses.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_110  
2025-07-28 11:29:43,217 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\reader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_25  
2025-07-28 11:29:43,218 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\console.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_4  
2025-07-28 11:29:43,219 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\contextvars.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_96  
2025-07-28 11:29:43,220 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\readers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_191  
2025-07-28 11:29:43,223 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\curses\has_key.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_109  
2025-07-28 11:29:43,223 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\sslproto.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_58  
2025-07-28 11:29:43,225 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ipaddress.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_41  
2025-07-28 11:29:43,225 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\completing_reader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_17  
2025-07-28 11:29:43,226 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ctypes\_endian.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_101  
2025-07-28 11:29:43,226 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_py_abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_15  
2025-07-28 11:29:43,236 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\constant.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_81  
2025-07-28 11:29:43,236 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\_functools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_168  
2025-07-28 11:29:43,237 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_174  
2025-07-28 11:29:43,238 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\windows_console.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_33  
2025-07-28 11:29:43,238 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_187  
2025-07-28 11:29:43,239 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\idna\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_153  
2025-07-28 11:29:43,239 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\_collections.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_167  
2025-07-28 11:29:43,240 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\curses.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_18  
2025-07-28 11:29:43,241 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\readers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_179  
2025-07-28 11:29:43,241 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_32  
2025-07-28 11:29:43,243 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_ios_support.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_11  
2025-07-28 11:29:43,244 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_distutils_hack\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_1  
2025-07-28 11:29:43,244 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_186  
2025-07-28 11:29:43,245 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\http\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_148  
2025-07-28 11:29:43,246 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\cd.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_80  
2025-07-28 11:29:43,247 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\idna\uts46data.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_158  
2025-07-28 11:29:43,249 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\historical_reader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_20  
2025-07-28 11:29:43,251 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_169  
2025-07-28 11:29:43,251 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\email\charset.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_121  
2025-07-28 11:29:43,252 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\json\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_35  
2025-07-28 11:29:43,252 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\datetime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_111  
2025-07-28 11:29:43,253 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\_common.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_175  
2025-07-28 11:29:43,254 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\bisect.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_73  
2025-07-28 11:29:43,254 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\json\decoder.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_118  
2025-07-28 11:29:43,256 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\json\encoder.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_36  
2025-07-28 11:29:43,256 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_188  
2025-07-28 11:29:43,257 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\constants.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_44  
2025-07-28 11:29:43,258 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\logging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_16  
2025-07-28 11:29:43,259 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\certifi\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_76  
2025-07-28 11:29:43,260 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\idna\core.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_154  
2025-07-28 11:29:43,260 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\backports\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_71  
2025-07-28 11:29:43,261 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\_text.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_171  
2025-07-28 11:29:43,262 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\types.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_29  
2025-07-28 11:29:43,262 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_185  
2025-07-28 11:29:43,263 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\asyncio\base_subprocess.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_42  
2025-07-28 11:29:43,263 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_distutils_hack\override.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_14  
2025-07-28 11:29:43,265 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\commands.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_5  
2025-07-28 11:29:43,265 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\_common.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_183  
2025-07-28 11:29:43,270 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_opcode_metadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_7  
2025-07-28 11:29:43,271 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\json\scanner.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_64  
2025-07-28 11:29:43,272 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\resources\abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_178  
2025-07-28 11:29:43,273 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib_resources\future\adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_190  
2025-07-28 11:29:43,275 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\jaraco.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_60  
2025-07-28 11:29:43,276 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\mimetypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_74  
2025-07-28 11:29:43,277 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\lzma.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_34  
2025-07-28 11:29:43,278 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\_pyrepl\unix_eventqueue.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_31  
2025-07-28 11:29:43,278 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\charset_normalizer\legacy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_82  
2025-07-28 11:29:43,279 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\importlib\metadata\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_166  
2025-07-28 11:29:43,287 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\context.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_49  
2025-07-28 11:29:43,289 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_13  
2025-07-28 11:29:43,290 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\dummy\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_85  
2025-07-28 11:29:43,290 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\forkserver.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_184  
2025-07-28 11:29:43,294 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_108  
2025-07-28 11:29:43,294 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\popen_fork.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_151  
2025-07-28 11:29:43,295 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\dummy\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_39  
2025-07-28 11:29:43,296 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\heap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_78  
2025-07-28 11:29:43,297 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\pool.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_38  
2025-07-28 11:29:43,298 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\managers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_0  
2025-07-28 11:29:43,298 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\popen_forkserver.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_90  
2025-07-28 11:29:43,301 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\popen_spawn_posix.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_68  
2025-07-28 11:29:43,302 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\queues.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_69  
2025-07-28 11:29:43,304 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\process.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_99  
2025-07-28 11:29:43,305 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\resource_sharer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_117  
2025-07-28 11:29:43,306 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\popen_spawn_win32.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_135  
2025-07-28 11:29:43,306 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\shared_memory.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_55  
2025-07-28 11:29:43,308 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\reduction.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_104  
2025-07-28 11:29:43,308 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\resource_tracker.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_120  
2025-07-28 11:29:43,312 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\sharedctypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_141  
2025-07-28 11:29:43,316 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\netrc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_130  
2025-07-28 11:29:43,317 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\spawn.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_105  
2025-07-28 11:29:43,319 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\opcode.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_149  
2025-07-28 11:29:43,319 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\nturl2path.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_136  
2025-07-28 11:29:43,322 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\numbers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_143  
2025-07-28 11:29:43,323 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\synchronize.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_155  
2025-07-28 11:29:43,325 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\_elffile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_51  
2025-07-28 11:29:43,325 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\multiprocessing\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_61  
2025-07-28 11:29:43,325 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_37  
2025-07-28 11:29:43,326 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\_musllinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_176  
2025-07-28 11:29:43,331 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_137  
2025-07-28 11:29:43,332 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\_manylinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_127  
2025-07-28 11:29:43,333 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\specifiers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_56  
2025-07-28 11:29:43,336 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\markers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_98  
2025-07-28 11:29:43,338 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\licenses\_spdx.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_131  
2025-07-28 11:29:43,338 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\_tokenizer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_3  
2025-07-28 11:29:43,339 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\_structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_140  
2025-07-28 11:29:43,340 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pathlib\_abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_87  
2025-07-28 11:29:43,342 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\platform.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_62  
2025-07-28 11:29:43,343 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\requirements.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_66  
2025-07-28 11:29:43,344 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pickle.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_88  
2025-07-28 11:29:43,345 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_150  
2025-07-28 11:29:43,346 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_45  
2025-07-28 11:29:43,346 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pathlib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_86  
2025-07-28 11:29:43,347 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pprint.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_22  
2025-07-28 11:29:43,348 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pydoc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_116  
2025-07-28 11:29:43,349 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\py_compile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_2  
2025-07-28 11:29:43,349 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pkgutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_93  
2025-07-28 11:29:43,350 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\licenses\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_172  
2025-07-28 11:29:43,351 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\packaging\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_43  
2025-07-28 11:29:43,354 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pathlib\_local.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_124  
2025-07-28 11:29:43,356 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\quopri.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_103  
2025-07-28 11:29:43,357 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\queue.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_189  
2025-07-28 11:29:43,358 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_59  
2025-07-28 11:29:43,359 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pydoc_data\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_53  
2025-07-28 11:29:43,366 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\pydoc_data\topics.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_129  
2025-07-28 11:29:43,366 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\__version__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_54  
2025-07-28 11:29:43,367 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\random.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_164  
2025-07-28 11:29:43,369 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\_internal_utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_180  
2025-07-28 11:29:43,370 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_8  
2025-07-28 11:29:43,371 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\auth.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_123  
2025-07-28 11:29:43,372 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\api.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_50  
2025-07-28 11:29:43,373 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\certs.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_132  
2025-07-28 11:29:43,376 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\compat.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_133  
2025-07-28 11:29:43,377 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_112  
2025-07-28 11:29:43,377 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\cookies.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_142  
2025-07-28 11:29:43,379 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\hooks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_113  
2025-07-28 11:29:43,382 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\models.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_152  
2025-07-28 11:29:43,383 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\sessions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_156  
2025-07-28 11:29:43,386 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\runpy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_126  
2025-07-28 11:29:43,388 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\packages.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_115  
2025-07-28 11:29:43,388 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\selectors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_181  
2025-07-28 11:29:43,389 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_159  
2025-07-28 11:29:43,390 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_119  
2025-07-28 11:29:43,393 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_63  
2025-07-28 11:29:43,395 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\secrets.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_94  
2025-07-28 11:29:43,397 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\requests\status_codes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_9  
2025-07-28 11:29:43,398 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\rlcompleter.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_91  
2025-07-28 11:29:43,399 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_core_metadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_146  
2025-07-28 11:29:43,401 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\_modified.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_97  
2025-07-28 11:29:43,405 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\command\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_128  
2025-07-28 11:29:43,406 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\archive_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_10  
2025-07-28 11:29:43,407 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_discovery.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_77  
2025-07-28 11:29:43,407 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\cmd.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_92  
2025-07-28 11:29:43,408 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_161  
2025-07-28 11:29:43,411 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\command\build.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_89  
2025-07-28 11:29:43,411 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\command\bdist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_24  
2025-07-28 11:29:43,411 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\_msvccompiler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_46  
2025-07-28 11:29:43,413 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\_log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_147  
2025-07-28 11:29:43,415 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\command\build_ext.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_125  
2025-07-28 11:29:43,416 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\command\sdist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_173  
2025-07-28 11:29:43,418 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\ccompiler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_57  
2025-07-28 11:29:43,420 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compat\numpy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_144  
2025-07-28 11:29:43,423 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compilers\C\msvc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_95  
2025-07-28 11:29:43,424 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\dir_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_100  
2025-07-28 11:29:43,427 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compilers\C\base.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_160  
2025-07-28 11:29:43,427 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compilers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_114  
2025-07-28 11:29:43,427 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\core.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_170  
2025-07-28 11:29:43,429 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_106  
2025-07-28 11:29:43,430 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_72  
2025-07-28 11:29:43,431 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_70  
2025-07-28 11:29:43,432 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compilers\C.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_48  
2025-07-28 11:29:43,434 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\file_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_163  
2025-07-28 11:29:43,435 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\debug.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_65  
2025-07-28 11:29:43,435 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\extension.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_75  
2025-07-28 11:29:43,437 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\dist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_30  
2025-07-28 11:29:43,437 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\compilers\C\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_83  
2025-07-28 11:29:43,438 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\fancy_getopt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_162  
2025-07-28 11:29:43,439 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_47  
2025-07-28 11:29:43,440 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\sysconfig.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_23  
2025-07-28 11:29:43,441 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\spawn.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_177  
2025-07-28 11:29:43,443 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\filelist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_122  
2025-07-28 11:29:43,444 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_26  
2025-07-28 11:29:43,445 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\text_file.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_102  
2025-07-28 11:29:43,447 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_157  
2025-07-28 11:29:43,448 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_distutils\versionpredicate.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_40  
2025-07-28 11:29:43,451 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_imp.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_84  
2025-07-28 11:29:43,452 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_normalization.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_145  
2025-07-28 11:29:43,454 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_entry_points.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_52  
2025-07-28 11:29:43,455 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_importlib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_21  
2025-07-28 11:29:43,464 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_shutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_165  
2025-07-28 11:29:43,465 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_139  
2025-07-28 11:29:43,466 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\backports\tarfile\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_19  
2025-07-28 11:29:43,467 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_path.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_79  
2025-07-28 11:29:43,468 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_reqs.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_27  
2025-07-28 11:29:43,473 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_static.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_134  
2025-07-28 11:29:43,474 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_collections.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_107  
2025-07-28 11:29:43,475 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\backports\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_138  
2025-07-28 11:29:43,476 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_12  
2025-07-28 11:29:43,478 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\backports\tarfile\compat\py38.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_67  
2025-07-28 11:29:43,480 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\backports\tarfile\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_6  
2025-07-28 11:29:43,480 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_182  
2025-07-28 11:29:43,481 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_4  
2025-07-28 11:29:43,483 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_compat.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_110  
2025-07-28 11:29:43,483 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_41  
2025-07-28 11:29:43,484 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_28  
2025-07-28 11:29:43,485 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_functools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_25  
2025-07-28 11:29:43,487 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_meta.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_96  
2025-07-28 11:29:43,490 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\compat\py311.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_58  
2025-07-28 11:29:43,490 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_109  
2025-07-28 11:29:43,490 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_text.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_191  
2025-07-28 11:29:43,493 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\jaraco\context.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_101  
2025-07-28 11:29:43,494 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\more_itertools\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_168  
2025-07-28 11:29:43,496 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\more_itertools\recipes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_33  
2025-07-28 11:29:43,497 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\jaraco.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_17  
2025-07-28 11:29:43,497 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\_elffile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_153  
2025-07-28 11:29:43,498 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_187  
2025-07-28 11:29:43,500 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\jaraco\text\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_81  
2025-07-28 11:29:43,502 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\more_itertools\more.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_174  
2025-07-28 11:29:43,505 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_179  
2025-07-28 11:29:43,505 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\jaraco\functools\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_15  
2025-07-28 11:29:43,506 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\_musllinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_18  
2025-07-28 11:29:43,508 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\_manylinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_167  
2025-07-28 11:29:43,511 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\markers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_1  
2025-07-28 11:29:43,512 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\specifiers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_148  
2025-07-28 11:29:43,514 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\_tokenizer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_11  
2025-07-28 11:29:43,517 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\_structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_32  
2025-07-28 11:29:43,517 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\requirements.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_186  
2025-07-28 11:29:43,518 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_20  
2025-07-28 11:29:43,519 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_80  
2025-07-28 11:29:43,520 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\tomli\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_169  
2025-07-28 11:29:43,525 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\packaging\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_158  
2025-07-28 11:29:43,526 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_118  
2025-07-28 11:29:43,529 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\typing_extensions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_175  
2025-07-28 11:29:43,530 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\tomli\_re.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_35  
2025-07-28 11:29:43,530 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\tomli\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_121  
2025-07-28 11:29:43,532 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_73  
2025-07-28 11:29:43,534 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\macosx_libfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_76  
2025-07-28 11:29:43,535 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\pack.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_188  
2025-07-28 11:29:43,536 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\metadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_154  
2025-07-28 11:29:43,537 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\convert.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_36  
2025-07-28 11:29:43,537 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\tomli\_types.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_111  
2025-07-28 11:29:43,539 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_44  
2025-07-28 11:29:43,540 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\unpack.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_16  
2025-07-28 11:29:43,543 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_musllinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_14  
2025-07-28 11:29:43,544 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_elffile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_185  
2025-07-28 11:29:43,546 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_71  
2025-07-28 11:29:43,548 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_5  
2025-07-28 11:29:43,549 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_29  
2025-07-28 11:29:43,550 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\specifiers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_190  
2025-07-28 11:29:43,552 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_manylinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_42  
2025-07-28 11:29:43,553 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_171  
2025-07-28 11:29:43,556 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\markers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_64  
2025-07-28 11:29:43,558 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_34  
2025-07-28 11:29:43,559 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_183  
2025-07-28 11:29:43,560 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\requirements.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_178  
2025-07-28 11:29:43,561 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_tokenizer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_7  
2025-07-28 11:29:43,564 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_60  
2025-07-28 11:29:43,566 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_74  
2025-07-28 11:29:43,567 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\archive_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_85  
2025-07-28 11:29:43,567 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\_requirestxt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_108  
2025-07-28 11:29:43,570 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\zipp\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_166  
2025-07-28 11:29:43,571 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\wheel\wheelfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_31  
2025-07-28 11:29:43,573 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\egg_info.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_38  
2025-07-28 11:29:43,573 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\bdist_wheel.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_39  
2025-07-28 11:29:43,575 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_184  
2025-07-28 11:29:43,576 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\zipp\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_82  
2025-07-28 11:29:43,577 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\zipp\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_13  
2025-07-28 11:29:43,579 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\_vendor\zipp\compat\py310.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_49  
2025-07-28 11:29:43,580 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\build.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_78  
2025-07-28 11:29:43,581 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_68  
2025-07-28 11:29:43,582 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_135  
2025-07-28 11:29:43,586 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\bdist_egg.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_151  
2025-07-28 11:29:43,586 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\compat\py310.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_69  
2025-07-28 11:29:43,588 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\compat\py311.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_99  
2025-07-28 11:29:43,588 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\sdist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_0  
2025-07-28 11:29:43,590 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\command\setopt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_90  
2025-07-28 11:29:43,590 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_validate_pyproject\extra_validations.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_141  
2025-07-28 11:29:43,593 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_117  
2025-07-28 11:29:43,594 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_validate_pyproject\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_104  
2025-07-28 11:29:43,595 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_apply_pyprojecttoml.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_55  
2025-07-28 11:29:43,596 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_validate_pyproject\fastjsonschema_validations.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_105  
2025-07-28 11:29:43,597 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_validate_pyproject\formats.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_149  
2025-07-28 11:29:43,599 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_validate_pyproject\error_reporting.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_120  
2025-07-28 11:29:43,602 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\pyprojecttoml.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_143  
2025-07-28 11:29:43,603 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\expand.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_136  
2025-07-28 11:29:43,603 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\discovery.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_61  
2025-07-28 11:29:43,605 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\setupcfg.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_155  
2025-07-28 11:29:43,606 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_176  
2025-07-28 11:29:43,607 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_127  
2025-07-28 11:29:43,608 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\config\_validate_pyproject\fastjsonschema_exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_130  
2025-07-28 11:29:43,609 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\depends.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_51  
2025-07-28 11:29:43,612 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\dist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_37  
2025-07-28 11:29:43,615 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\extension.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_137  
2025-07-28 11:29:43,615 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\installer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_56  
2025-07-28 11:29:43,619 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\warnings.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_62  
2025-07-28 11:29:43,619 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\logging.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_98  
2025-07-28 11:29:43,621 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\monkey.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_131  
2025-07-28 11:29:43,623 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\unicode_utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_140  
2025-07-28 11:29:43,625 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\msvc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_3  
2025-07-28 11:29:43,627 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_87  
2025-07-28 11:29:43,630 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\shlex.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_150  
2025-07-28 11:29:43,632 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\shutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_45  
2025-07-28 11:29:43,633 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\site.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_22  
2025-07-28 11:29:43,637 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\signal.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_86  
2025-07-28 11:29:43,638 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\windows_support.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_88  
2025-07-28 11:29:43,640 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\setuptools\wheel.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_66  
2025-07-28 11:29:43,641 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\ssl.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_93  
2025-07-28 11:29:43,645 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\string.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_43  
2025-07-28 11:29:43,645 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\socketserver.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_2  
2025-07-28 11:29:43,647 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\subprocess.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_103  
2025-07-28 11:29:43,648 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\stringprep.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_124  
2025-07-28 11:29:43,650 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\socket.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_116  
2025-07-28 11:29:43,651 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\statistics.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_172  
2025-07-28 11:29:43,652 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tarfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_59  
2025-07-28 11:29:43,654 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tempfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_53  
2025-07-28 11:29:43,654 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tkinter\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_164  
2025-07-28 11:29:43,656 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\threading.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_54  
2025-07-28 11:29:43,657 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\sysconfig\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_189  
2025-07-28 11:29:43,658 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\textwrap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_129  
2025-07-28 11:29:43,662 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tkinter\scrolledtext.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_50  
2025-07-28 11:29:43,663 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tkinter\commondialog.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_180  
2025-07-28 11:29:43,665 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tkinter\ttk.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_132  
2025-07-28 11:29:43,666 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tokenize.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_112  
2025-07-28 11:29:43,667 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tomllib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_142  
2025-07-28 11:29:43,668 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tkinter\constants.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_8  
2025-07-28 11:29:43,669 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\token.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_133  
2025-07-28 11:29:43,673 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tomllib\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_113  
2025-07-28 11:29:43,674 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tkinter\messagebox.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_123  
2025-07-28 11:29:43,676 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tracemalloc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_126  
2025-07-28 11:29:43,678 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\typing.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_181  
2025-07-28 11:29:43,679 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tomllib\_re.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_152  
2025-07-28 11:29:43,681 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tty.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_115  
2025-07-28 11:29:43,683 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_159  
2025-07-28 11:29:43,683 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\tomllib\_types.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_156  
2025-07-28 11:29:43,685 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\loader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_9  
2025-07-28 11:29:43,686 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\_log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_119  
2025-07-28 11:29:43,688 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\case.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_94  
2025-07-28 11:29:43,688 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\async_case.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_63  
2025-07-28 11:29:43,690 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_91  
2025-07-28 11:29:43,690 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\runner.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_128  
2025-07-28 11:29:43,691 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\result.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_97  
2025-07-28 11:29:43,692 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\mock.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_146  
2025-07-28 11:29:43,696 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\signals.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_10  
2025-07-28 11:29:43,697 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\suite.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_77  
2025-07-28 11:29:43,698 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib\error.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_89  
2025-07-28 11:29:43,699 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_161  
2025-07-28 11:29:43,700 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib\parse.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_24  
2025-07-28 11:29:43,701 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib\request.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_46  
2025-07-28 11:29:43,702 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\unittest\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_92  
2025-07-28 11:29:43,703 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_125  
2025-07-28 11:29:43,706 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_147  
2025-07-28 11:29:43,711 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\_request_methods.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_144  
2025-07-28 11:29:43,711 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\_base_connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_173  
2025-07-28 11:29:43,715 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_114  
2025-07-28 11:29:43,716 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\_collections.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_57  
2025-07-28 11:29:43,717 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_100  
2025-07-28 11:29:43,720 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\connectionpool.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_160  
2025-07-28 11:29:43,722 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\emscripten\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_106  
2025-07-28 11:29:43,723 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\emscripten\fetch.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_72  
2025-07-28 11:29:43,724 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\_version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_95  
2025-07-28 11:29:43,725 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\emscripten\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_170  
2025-07-28 11:29:43,726 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\emscripten\request.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_70  
2025-07-28 11:29:43,727 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\socks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_65  
2025-07-28 11:29:43,728 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\pyopenssl.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_163  
2025-07-28 11:29:43,730 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\contrib\emscripten\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_48  
2025-07-28 11:29:43,734 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\fields.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_30  
2025-07-28 11:29:43,735 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\http2\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_47  
2025-07-28 11:29:43,736 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_75  
2025-07-28 11:29:43,738 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\http2\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_162  
2025-07-28 11:29:43,738 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\filepost.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_83  
2025-07-28 11:29:43,740 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_26  
2025-07-28 11:29:43,743 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\http2\probe.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_23  
2025-07-28 11:29:43,744 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\poolmanager.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_177  
2025-07-28 11:29:43,746 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_102  
2025-07-28 11:29:43,747 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_122  
2025-07-28 11:29:43,748 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\request.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_40  
2025-07-28 11:29:43,749 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\ssl_match_hostname.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_21  
2025-07-28 11:29:43,750 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\proxy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_157  
2025-07-28 11:29:43,751 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\ssl_.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_52  
2025-07-28 11:29:43,752 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\retry.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_145  
2025-07-28 11:29:43,755 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\ssltransport.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_165  
2025-07-28 11:29:43,756 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\timeout.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_139  
2025-07-28 11:29:43,757 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\version_checker.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_134  
2025-07-28 11:29:43,758 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_84  
2025-07-28 11:29:43,760 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\url.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_19  
2025-07-28 11:29:43,761 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_138  
2025-07-28 11:29:43,761 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_79  
2025-07-28 11:29:43,762 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\webbrowser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_107  
2025-07-28 11:29:43,763 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\parsers\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_12  
2025-07-28 11:29:43,766 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\urllib3\util\wait.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_27  
2025-07-28 11:29:43,766 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\sax\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_6  
2025-07-28 11:29:43,767 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\sax\_exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_182  
2025-07-28 11:29:43,768 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\sax\expatreader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_4  
2025-07-28 11:29:43,770 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\parsers\expat.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_67  
2025-07-28 11:29:43,770 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\sax\xmlreader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_28  
2025-07-28 11:29:43,771 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\sax\saxutils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_41  
2025-07-28 11:29:43,772 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xml\sax\handler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_110  
2025-07-28 11:29:43,774 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xmlrpc\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_25  
2025-07-28 11:29:43,774 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\xmlrpc\client.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_96  
2025-07-28 11:29:43,777 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\zipimport.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_101  
2025-07-28 11:29:43,777 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\zipfile\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_58  
2025-07-28 11:29:43,779 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\zipfile\_path\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_109  
2025-07-28 11:29:43,780 [!] Exception on decompile bytecode file:decompiled_output\PYZ.pyz_extract\zipfile\_path\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 29040@ThreadPoolExecutor-0_191  
2025-07-28 11:29:44,753 [*] 
progress_check completed :__init__.py:102-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 29040@MainThread  
2025-07-28 11:29:44,753 [+] completed,cost 10450ms with result:94 arch file(s) handled. :__init__.py:167-20  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.statistics_status) at 29040@MainThread  
2025-07-28 11:29:53,983 [+] pydumpck is a multi-threads tool for decompile exe,elf,pyz,pyc packed by python which is base on pycdc and uncompyle6.sometimes its py-file result not exactly right ,maybe could use uncompyle6.
--------------------
pydumpck initilizing with 1.17.9 :pyinstaller_dump.py:115-20  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\pyinstaller_dump.py on(pyinstaller_dump.run) at 25172@MainThread  
2025-07-28 11:29:53,984 [*] plugins loaded with ['pycdc'] :__init__.py:155-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.load_plugins) at 25172@MainThread  
2025-07-28 11:29:53,984 [*] target file input:AugmentMagic.exe_extracted/main.pyc
to:decompiled_main :__init__.py:190-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.main) at 25172@MainThread  
2025-07-28 11:29:53,985 [*] start dump target file. type:pyc :__init__.py:203-10  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.main) at 25172@MainThread  
2025-07-28 11:29:56,990 [!] Exception on decompile bytecode file:decompiled_main\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 25172@MainThread  
2025-07-28 11:29:56,990 [+] completed,cost 3006ms with result:pyc file handled:decompiled_main\main.pyc :__init__.py:167-20  C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.statistics_status) at 25172@MainThread  
