using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AugmentMagicWPF.Services
{
    public interface IFileOperationService
    {
        Task<bool> CopyFileAsync(string sourcePath, string destinationPath);
        Task<bool> MoveFileAsync(string sourcePath, string destinationPath);
        Task<bool> DeleteFileAsync(string filePath);
        Task<string?> ReadTextFileAsync(string filePath);
        Task<bool> WriteTextFileAsync(string filePath, string content);
        Task<T?> ReadJsonFileAsync<T>(string filePath);
        Task<bool> WriteJsonFileAsync<T>(string filePath, T data);
        Task<bool> CreateZipArchiveAsync(string sourceDirectory, string zipFilePath);
        Task<bool> ExtractZipArchiveAsync(string zipFilePath, string extractDirectory);
        bool FileExists(string filePath);
        bool DirectoryExists(string directoryPath);
        void EnsureDirectoryExists(string directoryPath);
        long GetFileSize(string filePath);
        DateTime GetFileModifiedTime(string filePath);
        IEnumerable<string> GetFiles(string directoryPath, string searchPattern = "*", bool recursive = false);
    }
}
