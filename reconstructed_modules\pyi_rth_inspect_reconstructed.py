"""
pyi_rth_inspect - 从 .pyc 文件重构
"""

# 可能的类:
class ZipFile:
    """重构的类"""
    pass

# 可能的函数:
def getfile():
    """重构的函数"""
    pass

def join():
    """重构的函数"""
    pass

def _pyi_rthookr1():
    """重构的函数"""
    pass

def isabs():
    """重构的函数"""
    pass

def isfile():
    """重构的函数"""
    pass

def filename():
    """重构的函数"""
    pass

def object():
    """重构的函数"""
    pass

def SYS_PREFIX():
    """重构的函数"""
    pass

def pyc_filename():
    """重构的函数"""
    pass

def __main__():
    """重构的函数"""
    pass

def _get_base_library_files():
    """重构的函数"""
    pass

def _orig_inspect_getsourcefile():
    """重构的函数"""
    pass

def modules():
    """重构的函数"""
    pass

def base_library_files():
    """重构的函数"""
    pass

def zipfiles():
    """重构的函数"""
    pass

def path():
    """重构的函数"""
    pass

def BASE_LIBRARY():
    """重构的函数"""
    pass

def inspect():
    """重构的函数"""
    pass

def syss():
    """重构的函数"""
    pass

def _pyi_getsourcefile():
    """重构的函数"""
    pass

def main_file():
    """重构的函数"""
    pass

def basename():
    """重构的函数"""
    pass

def __file__():
    """重构的函数"""
    pass

def getatt():
    """重构的函数"""
    pass

def entry():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'base_library.zipc'
#   2: 'N#='
#   3: 'path'
#   4: 'normpath)'
#   5: 'entry'
#   6: 'oss'
#   7: ',PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py'
#   8: '<genexpr>'
#   9: '?_pyi_rthook.<locals>._get_base_library_files.<locals>.<genexpr>!'
#  10: 'isfile'
#  11: 'set'
#  12: 'ZipFile'
#  13: 'namelist)'
#  14: 'filename'
#  15: 'zfr'
#  16: 'zipfiles'
#  17: '_get_base_library_files'
#  18: ',_pyi_rthook.<locals>._get_base_library_files'
#  19: '__main__'
#  20: '__file__'
#  21: 'getfiler'
#  22: 'isabs'
#  23: 'getattr'
#  24: 'modules'
#  25: 'basename'
#  26: 'join'
#  27: 'startswith)'
#  28: 'objectr'
#  29: 'main_file'
#  30: 'pyc_filename'
#  31: 'BASE_LIBRARY'
#  32: 'SYS_PREFIX'
#  33: '_orig_inspect_getsourcefile'
#  34: 'base_library_files'
#  35: 'inspectr'
#  36: 'syss'
#  37: '_pyi_getsourcefile'
#  38: "'_pyi_rthook.<locals>._pyi_getsourcefile?"
#  39: '_MEIPASSr!'
#  40: 'getsourcefile)'
#  41: '  @@@@@@@@r'
#  42: '_pyi_rthookr1'
#  43: '<module>r3'