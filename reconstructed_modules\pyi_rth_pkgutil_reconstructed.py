"""
pyi_rth_pkgutil - 从 .pyc 文件重构
"""

# 可能的导入:
# iter_importer_modules)
# pyimod02_importerss

# 可能的函数:
def entry_data():
    """重构的函数"""
    pass

def _pyz_entry_prefix():
    """重构的函数"""
    pass

def registe():
    """重构的函数"""
    pass

def is_pkg():
    """重构的函数"""
    pass

def split():
    """重构的函数"""
    pass

def fallback_finde():
    """重构的函数"""
    pass

def pkg_name_parts():
    """重构的函数"""
    pass

def entry_name():
    """重构的函数"""
    pass

def items():
    """重构的函数"""
    pass

def prefix():
    """重构的函数"""
    pass

def pyz_toc_tree():
    """重构的函数"""
    pass

def pyimod02_importerss():
    """重构的函数"""
    pass

def get_pyz_toc_tree():
    """重构的函数"""
    pass

def _iter_pyi_frozen_finder_modules():
    """重构的函数"""
    pass

def dict():
    """重构的函数"""
    pass

def tree_node():
    """重构的函数"""
    pass

def pkgutil():
    """重构的函数"""
    pass

def finde():
    """重构的函数"""
    pass

def isinstance():
    """重构的函数"""
    pass

def pkg_name_part():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'pg['
#   2: 'get_pyz_toc_tree'
#   3: '_pyz_entry_prefix'
#   4: 'split'
#   5: 'get'
#   6: 'isinstance'
#   7: 'dict'
#   8: 'items'
#   9: 'fallback_finder'
#  10: 'iter_importer_modules)'
#  11: 'finder'
#  12: 'prefix'
#  13: 'pyz_toc_tree'
#  14: 'pkg_name_parts'
#  15: 'tree_node'
#  16: 'pkg_name_part'
#  17: 'entry_name'
#  18: 'entry_data'
#  19: 'is_pkg'
#  20: 'pkgutil'
#  21: 'pyimod02_importerss'
#  22: ',PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py'
#  23: '_iter_pyi_frozen_finder_modules'
#  24: '4_pyi_rthook.<locals>._iter_pyi_frozen_finder_modules'
#  25: 'A+C'
#  26: '2A#C'
#  27: 'register'
#  28: 'PyiFrozenFinder)'
#  29: ' @@r'
#  30: '_pyi_rthookr '
#  31: '<module>r#'