<Window x:Class="AugmentMagicWPF.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Augment Magic"
        Height="700"
        Width="900"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="White"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 成功按钮样式 -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1E7E34"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 危险按钮样式 -->
        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#BD2130"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 警告按钮样式 -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#FFC107"/>
            <Setter Property="Foreground" Value="#212529"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0A800"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#D39E00"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="10" BorderThickness="1" BorderBrush="#E0E0E0">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 自定义标题栏 -->
            <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="10,10,0,0"
                    MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid Height="40">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 应用图标和标题 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <Ellipse Width="20" Height="20" Fill="#007ACC" Margin="0,0,8,0"/>
                        <TextBlock Text="Augment Magic" FontSize="14" FontWeight="Medium"
                                 VerticalAlignment="Center" Foreground="#333"/>
                    </StackPanel>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Content="—" Width="40" Height="30" Background="Transparent"
                               BorderThickness="0" Foreground="#666" FontSize="14"
                               Click="MinimizeButton_Click"/>
                        <Button Content="✕" Width="40" Height="30" Background="Transparent"
                               BorderThickness="0" Foreground="#666" FontSize="12"
                               Click="CloseButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主内容区域 -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 系统信息卡片 -->
                <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Ellipse Grid.Column="0" Width="40" Height="40" Fill="#28A745"
                                VerticalAlignment="Top" Margin="0,0,15,0"/>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="系统信息" FontSize="16" FontWeight="SemiBold"
                                     Foreground="#333" Margin="0,0,0,8"/>
                            <TextBlock Text="系统: windows-x86_64" FontSize="13"
                                     Foreground="#666" Margin="0,0,0,4"/>
                            <TextBlock x:Name="AdminStatusTextBlock" Text="管理员权限: 是" FontSize="13"
                                     Foreground="#28A745" FontWeight="Medium"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 临时邮箱卡片 -->
                <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="临时邮箱" FontSize="16" FontWeight="SemiBold"
                                 Foreground="#333" Margin="0,0,0,15"/>

                        <!-- 邮箱地址行 -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="邮箱地址:" VerticalAlignment="Center"
                                     Margin="0,0,10,0" FontSize="13" Foreground="#666"/>
                            <TextBox x:Name="EmailTextBox" Grid.Column="1" Height="35"
                                   Margin="0,0,15,0" Style="{StaticResource ModernTextBox}" IsReadOnly="True"/>
                            <Button Grid.Column="2" Content="获取新邮箱" Height="35" Width="120"
                                  Style="{StaticResource ModernButton}" Click="GetNewEmailButton_Click"/>
                        </Grid>

                        <!-- 验证码行 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="验证码:" VerticalAlignment="Center"
                                     Margin="0,0,10,0" FontSize="13" Foreground="#666"/>
                            <TextBox x:Name="VerificationCodeTextBox" Grid.Column="1" Height="35"
                                   Margin="0,0,15,0" Style="{StaticResource ModernTextBox}" IsReadOnly="True"/>
                            <Button Grid.Column="2" Content="检查收件箱" Height="35" Width="120"
                                  Style="{StaticResource SuccessButton}" Click="CheckInboxButton_Click"
                                  Margin="0,0,10,0"/>
                            <Button Grid.Column="3" Content="复制验证码" Height="35" Width="100"
                                  Style="{StaticResource WarningButton}" Click="CopyCodeButton_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 执行控制卡片 -->
                <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="执行控制" FontSize="16" FontWeight="SemiBold"
                                 Foreground="#333" VerticalAlignment="Center"/>

                        <Button Grid.Column="1" Content="▶ 开始执行" Width="120" Height="35"
                              Margin="0,0,10,0" Style="{StaticResource SuccessButton}"
                              Click="StartExecutionButton_Click"/>
                        <Button Grid.Column="2" Content="⏹ 停止" Width="80" Height="35"
                              Margin="0,0,10,0" Style="{StaticResource DangerButton}"
                              Click="StopExecutionButton_Click"/>
                        <Button Grid.Column="3" Content="🗑 清除日志" Width="100" Height="35"
                              Margin="0,0,10,0" Style="{StaticResource WarningButton}"
                              Click="ClearLogButton_Click"/>
                        <Button Grid.Column="4" Content="ℹ 关于" Width="80" Height="35"
                              Style="{StaticResource ModernButton}" Click="AboutButton_Click"/>
                    </Grid>
                </Border>

                <!-- 日志区域 -->
                <Border Grid.Row="3" Background="White" CornerRadius="8" BorderThickness="1"
                       BorderBrush="#E0E0E0" Margin="0,0,0,15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 日志标题栏 -->
                        <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8,8,0,0"
                               Padding="15,10" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="📋 执行日志" FontSize="14"
                                         FontWeight="SemiBold" Foreground="#333"/>

                                <StackPanel Grid.Column="2" Orientation="Horizontal">
                                    <Ellipse x:Name="StatusIndicator" Width="8" Height="8"
                                           Fill="#28A745" Margin="0,0,8,0"/>
                                    <TextBlock x:Name="StatusTextBlock" Text="就绪" FontSize="12"
                                             Foreground="#666"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- 日志内容 -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Auto" Padding="15">
                            <TextBox x:Name="LogTextBox"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    IsReadOnly="True"
                                    FontFamily="Consolas"
                                    FontSize="12"
                                    TextWrapping="Wrap"
                                    Foreground="#333"
                                    MinHeight="200"/>
                        </ScrollViewer>
                    </Grid>
                </Border>

                <!-- 底部状态栏 -->
                <Border Grid.Row="5" Background="#F8F9FA" CornerRadius="6" Padding="15,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Augment Magic v1.0" FontSize="11"
                                 Foreground="#999"/>

                        <TextBlock Grid.Column="2" x:Name="TimeTextBlock" FontSize="11"
                                 Foreground="#999"/>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Border>
</Window>
