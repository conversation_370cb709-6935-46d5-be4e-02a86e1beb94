using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Windows;
using AugmentMagicWPF.Services;
using AugmentMagicWPF.ViewModels;
using AugmentMagicWPF.Views;

namespace AugmentMagicWPF
{
    public partial class App : Application
    {
        private IHost? _host;
        private static Mutex? _mutex;
        private const string MutexName = "AugmentMagicWPF_SingleInstance";

        protected override void OnStartup(StartupEventArgs e)
        {
            // 首先检查管理员权限
            if (!IsRunAsAdministrator())
            {
                ShowAdminRequiredDialog();
                Environment.Exit(0);
                return;
            }

            // Check for single instance
            bool createdNew;
            _mutex = new Mutex(true, MutexName, out createdNew);

            if (!createdNew)
            {
                // Another instance is already running
                MessageBox.Show("Augment Magic 已经在运行中。", "应用程序已启动", MessageBoxButton.OK, MessageBoxImage.Information);
                Environment.Exit(0);
                return;
            }

            try
            {
                base.OnStartup(e);

                // Create a simple main window without complex dependencies
                var mainWindow = new MainWindow();
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动错误: {ex.Message}\n\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 检查当前程序是否以管理员权限运行
        /// </summary>
        /// <returns>如果是管理员权限返回true，否则返回false</returns>
        private static bool IsRunAsAdministrator()
        {
            try
            {
                WindowsIdentity identity = WindowsIdentity.GetCurrent();
                WindowsPrincipal principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 显示需要管理员权限的对话框，并提供重新以管理员身份启动的选项
        /// </summary>
        private static void ShowAdminRequiredDialog()
        {
            var result = MessageBox.Show(
                "Augment Magic 需要管理员权限才能正常运行。\n\n" +
                "这是因为程序需要执行系统级操作和访问受保护的资源。\n\n" +
                "是否要以管理员身份重新启动程序？",
                "需要管理员权限 - Augment Magic",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning,
                MessageBoxResult.Yes);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 获取当前可执行文件的路径
                    string exePath = Process.GetCurrentProcess().MainModule?.FileName ?? "";

                    if (!string.IsNullOrEmpty(exePath))
                    {
                        // 以管理员身份重新启动程序
                        ProcessStartInfo startInfo = new ProcessStartInfo
                        {
                            FileName = exePath,
                            UseShellExecute = true,
                            Verb = "runas" // 请求管理员权限
                        };

                        Process.Start(startInfo);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"无法以管理员身份启动程序：\n\n{ex.Message}\n\n" +
                        "请手动右键点击程序图标，选择\"以管理员身份运行\"。",
                        "启动失败",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                _host?.Dispose();
                _mutex?.ReleaseMutex();
                _mutex?.Dispose();
            }
            catch
            {
                // Ignore errors during cleanup
            }
            finally
            {
                base.OnExit(e);
            }
        }
    }
}
