using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace AugmentMagicWPF.Services
{
    public class FileOperationService : IFileOperationService
    {
        public async Task<bool> CopyFileAsync(string sourcePath, string destinationPath)
        {
            try
            {
                EnsureDirectoryExists(Path.GetDirectoryName(destinationPath)!);
                
                using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read);
                using var destinationStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write);
                
                await sourceStream.CopyToAsync(destinationStream);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> MoveFileAsync(string sourcePath, string destinationPath)
        {
            try
            {
                EnsureDirectoryExists(Path.GetDirectoryName(destinationPath)!);
                
                if (await CopyFileAsync(sourcePath, destinationPath))
                {
                    File.Delete(sourcePath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                await Task.Run(() => File.Delete(filePath));
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string?> ReadTextFileAsync(string filePath)
        {
            try
            {
                return await File.ReadAllTextAsync(filePath);
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> WriteTextFileAsync(string filePath, string content)
        {
            try
            {
                EnsureDirectoryExists(Path.GetDirectoryName(filePath)!);
                await File.WriteAllTextAsync(filePath, content);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<T?> ReadJsonFileAsync<T>(string filePath)
        {
            try
            {
                var json = await ReadTextFileAsync(filePath);
                if (string.IsNullOrEmpty(json))
                    return default;

                return JsonSerializer.Deserialize<T>(json);
            }
            catch
            {
                return default;
            }
        }

        public async Task<bool> WriteJsonFileAsync<T>(string filePath, T data)
        {
            try
            {
                var json = JsonSerializer.Serialize(data, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                return await WriteTextFileAsync(filePath, json);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CreateZipArchiveAsync(string sourceDirectory, string zipFilePath)
        {
            try
            {
                EnsureDirectoryExists(Path.GetDirectoryName(zipFilePath)!);
                
                await Task.Run(() => ZipFile.CreateFromDirectory(sourceDirectory, zipFilePath));
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ExtractZipArchiveAsync(string zipFilePath, string extractDirectory)
        {
            try
            {
                EnsureDirectoryExists(extractDirectory);
                
                await Task.Run(() => ZipFile.ExtractToDirectory(zipFilePath, extractDirectory));
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool FileExists(string filePath)
        {
            return File.Exists(filePath);
        }

        public bool DirectoryExists(string directoryPath)
        {
            return Directory.Exists(directoryPath);
        }

        public void EnsureDirectoryExists(string directoryPath)
        {
            if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        public long GetFileSize(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch
            {
                return 0;
            }
        }

        public DateTime GetFileModifiedTime(string filePath)
        {
            try
            {
                return File.GetLastWriteTime(filePath);
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        public IEnumerable<string> GetFiles(string directoryPath, string searchPattern = "*", bool recursive = false)
        {
            try
            {
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                return Directory.GetFiles(directoryPath, searchPattern, searchOption);
            }
            catch
            {
                return Enumerable.Empty<string>();
            }
        }
    }
}
