#!/usr/bin/env python3
import dis
import marshal
import struct
import sys
import os

def analyze_pyc_file(pyc_path):
    """分析 .pyc 文件并尝试提取字节码"""
    try:
        with open(pyc_path, 'rb') as f:
            # 读取文件头
            magic = f.read(4)
            print(f"Magic bytes: {magic.hex()}")
            
            # 尝试读取时间戳和大小信息
            timestamp = f.read(4)
            size = f.read(4)
            print(f"Timestamp: {timestamp.hex()}")
            print(f"Size: {size.hex()}")
            
            # 尝试读取更多头部信息（Python 3.7+）
            extra = f.read(4)
            print(f"Extra: {extra.hex()}")
            
            # 读取剩余的字节码
            bytecode = f.read()
            print(f"Bytecode length: {len(bytecode)}")
            
            # 尝试反序列化代码对象
            try:
                code_obj = marshal.loads(bytecode)
                print("Successfully loaded code object!")
                
                # 反汇编字节码
                print("\n=== DISASSEMBLY ===")
                dis.dis(code_obj)
                
                # 尝试获取源代码信息
                print(f"\nFilename: {code_obj.co_filename}")
                print(f"Function name: {code_obj.co_name}")
                print(f"Constants: {code_obj.co_consts}")
                print(f"Names: {code_obj.co_names}")
                print(f"Variable names: {code_obj.co_varnames}")
                
                return code_obj
                
            except Exception as e:
                print(f"Failed to load code object: {e}")
                
                # 尝试不同的偏移量
                for offset in [12, 16, 20]:
                    try:
                        f.seek(offset)
                        bytecode = f.read()
                        code_obj = marshal.loads(bytecode)
                        print(f"Success with offset {offset}!")
                        dis.dis(code_obj)
                        return code_obj
                    except:
                        continue
                        
    except Exception as e:
        print(f"Error reading file: {e}")
    
    return None

def reconstruct_python_code(code_obj):
    """尝试从代码对象重构 Python 代码"""
    if not code_obj:
        return None
        
    lines = []
    
    # 添加导入语句（基于 co_names）
    imports = []
    for name in code_obj.co_names:
        if name in ['tkinter', 'ttk', 'messagebox', 'scrolledtext', 'Path', 'datetime', 'files', 'TempMailClient', 'VersionChecker']:
            imports.append(name)
    
    if 'tkinter' in imports:
        lines.append("import tkinter as tk")
        lines.append("from tkinter import ttk, messagebox, scrolledtext")
    
    if 'Path' in imports:
        lines.append("from pathlib import Path")
    
    if 'datetime' in imports:
        lines.append("from datetime import datetime")
        
    # 其他可能的导入
    other_imports = ['files', 'TempMailClient', 'VersionChecker']
    for imp in other_imports:
        if imp in imports:
            lines.append(f"from {imp.lower()} import {imp}")
    
    lines.append("")
    
    # 添加函数定义（基于常量和字节码分析）
    if code_obj.co_consts:
        for const in code_obj.co_consts:
            if isinstance(const, str) and "获取系统信息" in const:
                lines.append("def get_system_info():")
                lines.append('    """获取系统信息，返回操作系统和架构"""')
                lines.append("    import platform")
                lines.append("    system = platform.system().lower()")
                lines.append("    arch = platform.machine().lower()")
                lines.append("    # 系统名称映射")
                lines.append("    if system == 'darwin':")
                lines.append("        system = 'macos'")
                lines.append("    elif system == 'windows':")
                lines.append("        system = 'windows'")
                lines.append("    else:")
                lines.append("        system = 'linux'")
                lines.append("    # 架构映射")
                lines.append("    if arch in ['x86_64', 'amd64']:")
                lines.append("        arch = 'x86_64'")
                lines.append("    elif arch in ['arm64', 'aarch64']:")
                lines.append("        arch = 'aarch64'")
                lines.append("    return system, arch")
                lines.append("")
                break
    
    return "\n".join(lines)

if __name__ == "__main__":
    pyc_files = [
        "AugmentMagic.exe_extracted/main.pyc",
        "AugmentMagic.exe_extracted/struct.pyc",
        "AugmentMagic.exe_extracted/pyiboot01_bootstrap.pyc"
    ]
    
    for pyc_file in pyc_files:
        if os.path.exists(pyc_file):
            print(f"\n{'='*50}")
            print(f"Analyzing: {pyc_file}")
            print('='*50)
            
            code_obj = analyze_pyc_file(pyc_file)
            
            if code_obj:
                # 尝试重构代码
                reconstructed = reconstruct_python_code(code_obj)
                if reconstructed:
                    output_file = pyc_file.replace('.pyc', '_reconstructed.py')
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(reconstructed)
                    print(f"Reconstructed code saved to: {output_file}")
