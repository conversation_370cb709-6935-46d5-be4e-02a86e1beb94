"""
pyiboot01_bootstrap - 从 .pyc 文件重构
"""

# 可能的导入:
# pyimod02_importers
# ImportError

# 可能的类:
class VIRTENV:
    """重构的类"""
    pass

class ImportError:
    """重构的类"""
    pass

# 可能的函数:
def abspath():
    """重构的函数"""
    pass

def _MEIPASS():
    """重构的函数"""
    pass

def append():
    """重构的函数"""
    pass

def exec_prefix():
    """重构的函数"""
    pass

def join():
    """重构的函数"""
    pass

def isdi():
    """重构的函数"""
    pass

def startswith():
    """重构的函数"""
    pass

def prefix():
    """重构的函数"""
    pass

def install():
    """重构的函数"""
    pass

def endswith():
    """重构的函数"""
    pass

def platform():
    """重构的函数"""
    pass

def hasattr():
    """重构的函数"""
    pass

def warnoptions():
    """重构的函数"""
    pass

def base_prefix():
    """重构的函数"""
    pass

def base_exec_prefix():
    """重构的函数"""
    pass

def python_path():
    """重构的函数"""
    pass

def encodings():
    """重构的函数"""
    pass

def pyimod03_ctypes():
    """重构的函数"""
    pass

def winz():
    """重构的函数"""
    pass

def warnings():
    """重构的函数"""
    pass

def frozenT():
    """重构的函数"""
    pass

def path():
    """重构的函数"""
    pass

def listdi():
    """重构的函数"""
    pass

def pyimod02_importers():
    """重构的函数"""
    pass

def environ():
    """重构的函数"""
    pass

def pyimod04_pywin32():
    """重构的函数"""
    pass

def VIRTUAL_ENV():
    """重构的函数"""
    pass

def entry():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'frozenT'
#   2: 'VIRTUAL_ENV'
#   3: 'winz'
#   4: '.egg)'
#   5: 'sys'
#   6: 'pyimod02_importers'
#   7: 'install'
#   8: 'hasattrr'
#   9: '_MEIPASS'
#  10: 'prefix'
#  11: 'exec_prefix'
#  12: 'base_prefix'
#  13: 'base_exec_prefix'
#  14: 'VIRTENV'
#  15: 'environ'
#  16: 'python_path'
#  17: 'path'
#  18: 'pth'
#  19: 'append'
#  20: 'abspath'
#  21: 'encodings'
#  22: 'ImportError'
#  23: 'warnoptions'
#  24: 'warnings'
#  25: 'pyimod03_ctypes'
#  26: 'platform'
#  27: 'startswith'
#  28: 'pyimod04_pywin32'
#  29: 'listdir'
#  30: 'entry'
#  31: 'join'
#  32: 'isdir'
#  33: 'endswith'
#  34: ')PyInstaller\\loader\\pyiboot01_bootstrap.py'
#  35: '<module>r('