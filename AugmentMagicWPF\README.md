# Augment Magic WPF

一个现代化的 WPF 应用程序，具有科技风格的界面设计，复制了从 Python 反编译项目中提取的所有功能。

## 功能特性

### 🎨 界面设计
- **科技风格**: 深色主题配合霓虹蓝色调
- **Material Design**: 使用 Material Design 组件
- **自定义窗口**: 无边框窗口设计，自定义标题栏
- **动画效果**: 流畅的过渡动画和悬停效果
- **响应式布局**: 支持窗口缩放和自适应

### 🔧 核心功能
- **系统信息显示**: 实时显示操作系统、架构、管理员权限等信息
- **临时邮箱管理**: 获取临时邮箱地址，检查收件箱，提取验证码
- **任务执行器**: 执行系统任务，显示实时进度和日志
- **版本检查**: 自动检查软件更新
- **日志系统**: 彩色分级日志显示，支持保存和清除
- **文件操作**: 完整的文件管理功能

### 🏗️ 技术架构
- **.NET 8.0**: 最新的 .NET 框架
- **WPF**: Windows Presentation Foundation
- **MVVM 模式**: Model-View-ViewModel 架构
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **异步编程**: 全面使用 async/await 模式

## 项目结构

```
AugmentMagicWPF/
├── Views/                  # 视图文件
│   ├── MainWindow.xaml     # 主窗口
│   └── MainWindow.xaml.cs  # 主窗口代码
├── ViewModels/             # 视图模型
│   ├── BaseViewModel.cs    # 基础视图模型
│   ├── MainWindowViewModel.cs
│   ├── SystemInfoViewModel.cs
│   ├── TempMailViewModel.cs
│   └── ...
├── Models/                 # 数据模型
│   ├── SystemInfo.cs
│   ├── LogEntry.cs
│   ├── EmailMessage.cs
│   └── ...
├── Services/               # 服务层
│   ├── ISystemInfoService.cs
│   ├── SystemInfoService.cs
│   ├── ITempMailService.cs
│   ├── TempMailService.cs
│   └── ...
├── Styles/                 # 样式文件
│   └── CustomStyles.xaml  # 自定义样式
├── Resources/              # 资源文件
│   ├── Icons/             # 图标
│   ├── Images/            # 图片
│   └── Data/              # 数据文件
└── App.xaml               # 应用程序入口
```

## 安装和运行

### 前提条件
- .NET 8.0 SDK
- Visual Studio 2022 或 Visual Studio Code
- Windows 10/11

### 构建项目
```bash
# 克隆项目
cd AugmentMagicWPF

# 还原 NuGet 包
dotnet restore

# 构建项目
dotnet build

# 运行项目
dotnet run
```

### 发布项目
```bash
# 发布为单文件可执行程序
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 发布为框架依赖程序
dotnet publish -c Release -r win-x64 --self-contained false
```

## 使用说明

### 主界面功能

1. **系统信息面板**
   - 显示当前系统信息
   - 管理员权限状态指示
   - 实时系统状态

2. **临时邮箱功能**
   - 点击"获取新邮箱"生成临时邮箱地址
   - 点击"检查收件箱"查看收到的邮件
   - 自动提取验证码

3. **执行控制**
   - "开始执行"启动主要任务
   - "停止执行"中断当前任务
   - "清除日志"清空日志显示

4. **日志系统**
   - 彩色分级日志显示
   - 实时滚动更新
   - 支持保存到文件

### 快捷键
- `Ctrl+S`: 保存日志
- `Ctrl+L`: 清除日志
- `F5`: 刷新系统信息
- `Alt+F4`: 退出应用程序

## 自定义和扩展

### 添加新功能
1. 在 `Services/` 目录创建新的服务接口和实现
2. 在 `ViewModels/` 目录创建对应的视图模型
3. 在 `Views/` 目录创建用户界面
4. 在 `App.xaml.cs` 中注册依赖注入

### 修改样式
- 编辑 `Styles/CustomStyles.xaml` 修改界面样式
- 编辑 `App.xaml` 修改全局颜色和主题

### 添加新的日志级别
1. 在 `Models/LogEntry.cs` 中添加新的 `LogLevel` 枚举值
2. 在 `Services/ILoggerService.cs` 中添加新的日志方法
3. 在 `Services/LoggerService.cs` 中实现新方法

## 依赖包

- **MaterialDesignThemes**: Material Design 组件
- **ModernWpfUI**: 现代 WPF UI 组件
- **Microsoft.Extensions.DependencyInjection**: 依赖注入
- **Microsoft.Extensions.Hosting**: 主机服务
- **Newtonsoft.Json**: JSON 序列化

## 故障排除

### 常见问题

1. **应用程序无法启动**
   - 确保安装了 .NET 8.0 运行时
   - 检查依赖包是否正确还原

2. **界面显示异常**
   - 检查 XAML 文件语法
   - 确保所有资源文件存在

3. **功能无法正常工作**
   - 检查服务是否正确注册
   - 查看日志输出获取错误信息

### 调试模式
在 Debug 模式下运行可以获取更详细的错误信息：
```bash
dotnet run --configuration Debug
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现所有核心功能
- 科技风格界面设计
- 完整的 MVVM 架构
