<Application x:Class="AugmentMagicWPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Modern WPF -->
                <ui:ThemeResources RequestedTheme="Dark" />
                <ui:XamlControlsResources />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Styles/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Colors -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#6200EA"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#BB86FC"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#121212"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#1E1E1E"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#CF6679"/>
            <SolidColorBrush x:Key="OnPrimaryBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="OnSecondaryBrush" Color="#000000"/>
            <SolidColorBrush x:Key="OnBackgroundBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="OnSurfaceBrush" Color="#FFFFFF"/>

            <!-- Gradient Brushes -->
            <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#6200EA" Offset="0"/>
                <GradientStop Color="#3700B3" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#03DAC6" Offset="0"/>
                <GradientStop Color="#018786" Offset="1"/>
            </LinearGradientBrush>

            <!-- Cyber/Tech Style Brushes -->
            <LinearGradientBrush x:Key="CyberGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#00D4FF" Offset="0"/>
                <GradientStop Color="#0099CC" Offset="0.5"/>
                <GradientStop Color="#0066FF" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="NeonGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF00FF" Offset="0"/>
                <GradientStop Color="#00FFFF" Offset="0.5"/>
                <GradientStop Color="#FFFF00" Offset="1"/>
            </LinearGradientBrush>

            <!-- Animation Resources -->
            <Storyboard x:Key="FadeInAnimation">
                <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                               From="0" To="1" Duration="0:0:0.3"/>
            </Storyboard>

            <Storyboard x:Key="SlideInFromLeftAnimation">
                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                               From="-100" To="0" Duration="0:0:0.4">
                    <DoubleAnimation.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </DoubleAnimation.EasingFunction>
                </DoubleAnimation>
            </Storyboard>

            <!-- Global Font Settings -->
            <FontFamily x:Key="PrimaryFont">Segoe UI</FontFamily>
            <FontFamily x:Key="MonospaceFont">Consolas</FontFamily>
            <FontFamily x:Key="TechFont">Orbitron</FontFamily>

            <!-- Converters -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        </ResourceDictionary>
    </Application.Resources>
</Application>
