#!/usr/bin/env python3
import os
import re

def hex_dump(data, offset=0, length=None):
    """创建十六进制转储"""
    if length is None:
        length = len(data)
    
    lines = []
    for i in range(0, min(length, len(data)), 16):
        hex_part = ' '.join(f'{b:02x}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        lines.append(f'{offset+i:08x}: {hex_part:<48} |{ascii_part}|')
    
    return '\n'.join(lines)

def find_strings(data, min_length=4):
    """在二进制数据中查找可打印字符串"""
    strings = []
    current_string = ""
    
    for byte in data:
        if 32 <= byte <= 126:  # 可打印ASCII字符
            current_string += chr(byte)
        else:
            if len(current_string) >= min_length:
                strings.append(current_string)
            current_string = ""
    
    if len(current_string) >= min_length:
        strings.append(current_string)
    
    return strings

def analyze_pyc_structure(file_path):
    """分析 .pyc 文件结构"""
    print(f"Analyzing: {file_path}")
    print("="*60)
    
    with open(file_path, 'rb') as f:
        data = f.read()
    
    print(f"File size: {len(data)} bytes")
    print()
    
    # 显示文件头
    print("File header (first 64 bytes):")
    print(hex_dump(data[:64]))
    print()
    
    # 查找字符串
    strings = find_strings(data, min_length=3)
    print("Found strings:")
    for i, s in enumerate(strings[:50]):  # 只显示前50个字符串
        print(f"  {i+1:2d}: {repr(s)}")
    print()
    
    # 查找可能的Python关键字和函数名
    python_keywords = [
        'def ', 'class ', 'import ', 'from ', 'if ', 'else:', 'elif ', 'for ', 'while ',
        'try:', 'except:', 'finally:', 'with ', 'return ', 'yield ', 'lambda ',
        '__init__', '__main__', 'self', 'True', 'False', 'None'
    ]
    
    found_keywords = []
    for keyword in python_keywords:
        if keyword.encode() in data:
            found_keywords.append(keyword)
    
    if found_keywords:
        print("Found Python keywords/patterns:")
        for kw in found_keywords:
            print(f"  - {repr(kw)}")
        print()
    
    # 查找可能的函数定义
    function_pattern = re.compile(rb'def\s+(\w+)\s*\(')
    functions = function_pattern.findall(data)
    if functions:
        print("Possible function definitions:")
        for func in functions:
            print(f"  - {func.decode('utf-8', errors='ignore')}")
        print()
    
    # 查找导入语句
    import_pattern = re.compile(rb'(?:import|from)\s+(\w+)')
    imports = import_pattern.findall(data)
    if imports:
        print("Possible imports:")
        for imp in set(imports):
            print(f"  - {imp.decode('utf-8', errors='ignore')}")
        print()

def extract_readable_code(file_path):
    """尝试提取可读的代码片段"""
    with open(file_path, 'rb') as f:
        data = f.read()
    
    # 查找所有字符串
    strings = find_strings(data, min_length=10)
    
    # 过滤出可能是代码的字符串
    code_strings = []
    for s in strings:
        if any(keyword in s for keyword in ['def ', 'class ', 'import ', 'from ', 'if ', 'return ']):
            code_strings.append(s)
    
    return code_strings

if __name__ == "__main__":
    pyc_files = [
        "AugmentMagic.exe_extracted/main.pyc",
        "AugmentMagic.exe_extracted/struct.pyc"
    ]
    
    for pyc_file in pyc_files:
        if os.path.exists(pyc_file):
            analyze_pyc_structure(pyc_file)
            
            # 尝试提取代码
            code_strings = extract_readable_code(pyc_file)
            if code_strings:
                print("Possible code fragments:")
                for i, code in enumerate(code_strings):
                    print(f"  {i+1}: {repr(code)}")
            
            print("\n" + "="*80 + "\n")
