using System;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Navigation;
using System.Windows.Threading;
using AugmentMagicWPF.Services;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Views
{
    public partial class MainWindow : Window
    {
        private DispatcherTimer _timeTimer;
        private bool _isExecuting = false;
        private ITempMailService _tempMailService;
        private string _currentEmail = string.Empty;

        public MainWindow()
        {
            InitializeComponent();
            InitializeAdminStatus();
            InitializeLog();
            InitializeTimer();
            InitializeTempMailService();
        }

        private void InitializeAdminStatus()
        {
            // 更新界面上的管理员权限显示
            bool isAdmin = IsRunAsAdministrator();
            AdminStatusTextBlock.Text = $"管理员权限: {(isAdmin ? "是" : "否")}";
            AdminStatusTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(
                isAdmin ? System.Windows.Media.Color.FromRgb(40, 167, 69) : System.Windows.Media.Color.FromRgb(220, 53, 69));
        }

        private void InitializeLog()
        {
            // 初始化日志显示，与原程序一致
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            bool isAdmin = IsRunAsAdministrator();

            LogTextBox.Text = $"[{timestamp}] 🚀 Augment Magic 启动中...\n" +
                             $"[{timestamp}] 💻 系统信息：windows-x86_64\n" +
                             $"[{timestamp}] 🔑 管理员权限：{(isAdmin ? "是" : "否")} {(isAdmin ? "✅" : "❌")}\n" +
                             $"[{timestamp}] 🔍 正在检查目标文件...\n" +
                             $"[{timestamp}] 🔄 检查更新中...\n" +
                             $"[{timestamp}] ✅ 当前版本是最新的\n" +
                             $"[{timestamp}] 🚀 程序初始化完成，等待用户操作";

            UpdateStatus("就绪", "#28A745");
        }

        /// <summary>
        /// 检查当前程序是否以管理员权限运行
        /// </summary>
        private static bool IsRunAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        private void InitializeTimer()
        {
            _timeTimer = new DispatcherTimer();
            _timeTimer.Interval = TimeSpan.FromSeconds(1);
            _timeTimer.Tick += (s, e) => TimeTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
            _timeTimer.Start();
        }

        private void InitializeTempMailService()
        {
            _tempMailService = new TempMailService();
        }

        private void AddLog(string message, string level = "INFO")
        {
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            string icon = level switch
            {
                "ERROR" => "❌",
                "WARNING" => "⚠️",
                "SUCCESS" => "✅",
                "INFO" => "ℹ️",
                _ => "📝"
            };

            LogTextBox.Text += $"\n[{timestamp}] {icon} {message}";
            LogTextBox.ScrollToEnd();
        }

        private void UpdateStatus(string status, string color = "#666")
        {
            StatusTextBlock.Text = status;

            // 更新状态指示器颜色
            var brush = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(color));
            StatusIndicator.Fill = brush;
        }

        // 窗口控制功能
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        // 获取新邮箱按钮 - 与原程序的get_new_email功能一致
        private async void GetNewEmailButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("获取新邮箱...", "#FFC107");
                AddLog("正在获取新的临时邮箱...");

                // 使用TempMailService获取邮箱
                string email = await _tempMailService.GetNewEmailAsync();
                _currentEmail = email;

                EmailTextBox.Text = email;
                VerificationCodeTextBox.Text = ""; // 清空验证码
                AddLog($"获取到新邮箱: {email}");
                UpdateStatus("就绪", "#28A745");
            }
            catch (Exception ex)
            {
                AddLog($"获取邮箱失败: {ex.Message}", "ERROR");
                UpdateStatus("错误", "#DC3545");
            }
        }

        // 检查收件箱按钮 - 新增验证码获取功能
        private async void CheckInboxButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentEmail))
            {
                AddLog("请先获取邮箱地址", "WARNING");
                return;
            }

            try
            {
                UpdateStatus("检查收件箱...", "#FFC107");
                AddLog("正在检查收件箱...");

                var emails = await _tempMailService.CheckInboxAsync(_currentEmail);

                if (emails.Any())
                {
                    AddLog($"收到 {emails.Count()} 封邮件", "SUCCESS");

                    string foundCode = string.Empty;
                    foreach (var email in emails)
                    {
                        AddLog($"邮件: {email.Subject} - {email.From}");

                        // 尝试提取验证码
                        var code = _tempMailService.ExtractVerificationCode(email.Body);
                        if (!string.IsNullOrEmpty(code))
                        {
                            foundCode = code;
                            AddLog($"找到验证码: {code}", "SUCCESS");
                            break;
                        }
                    }

                    if (!string.IsNullOrEmpty(foundCode))
                    {
                        VerificationCodeTextBox.Text = foundCode;
                        UpdateStatus("验证码已获取", "#28A745");
                    }
                    else
                    {
                        AddLog("未在邮件中找到验证码", "WARNING");
                        UpdateStatus("未找到验证码", "#FFC107");
                    }
                }
                else
                {
                    AddLog("收件箱为空，请稍后再试");
                    UpdateStatus("收件箱为空", "#FFC107");
                }
            }
            catch (Exception ex)
            {
                AddLog($"检查收件箱失败: {ex.Message}", "ERROR");
                UpdateStatus("检查失败", "#DC3545");
            }
        }

        // 复制验证码按钮
        private void CopyCodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string code = VerificationCodeTextBox.Text;
                if (!string.IsNullOrEmpty(code))
                {
                    Clipboard.SetText(code);
                    AddLog($"验证码已复制到剪贴板: {code}", "SUCCESS");
                    UpdateStatus("验证码已复制", "#28A745");
                }
                else
                {
                    AddLog("没有验证码可复制", "WARNING");
                }
            }
            catch (Exception ex)
            {
                AddLog($"复制失败: {ex.Message}", "ERROR");
            }
        }

        // 开始执行按钮 - 与原程序的start_execution功能一致，增强错误处理
        private async void StartExecutionButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isExecuting)
            {
                AddLog("执行已在进行中，请等待当前任务完成", "WARNING");
                return;
            }

            try
            {
                _isExecuting = true;
                UpdateStatus("执行中...", "#FFC107");
                AddLog("🚀 开始执行任务...", "INFO");

                // 详细的路径查找和错误报告
                string executablePath = GetExecutablePath();

                if (string.IsNullOrEmpty(executablePath))
                {
                    AddLog("❌ 错误: 未找到可执行文件", "ERROR");
                    AddLog("🔍 查找的文件名: augment-magic-windows-x86_64.exe", "INFO");
                    AddLog("📁 已搜索的路径:", "INFO");

                    // 显示所有搜索过的路径
                    string[] searchPaths = GetSearchPaths();
                    for (int i = 0; i < searchPaths.Length; i++)
                    {
                        bool exists = System.IO.File.Exists(searchPaths[i]);
                        AddLog($"   {i + 1}. {searchPaths[i]} {(exists ? "✅" : "❌")}", exists ? "SUCCESS" : "ERROR");
                    }

                    AddLog("💡 解决方案: 请确保可执行文件位于正确的路径中", "INFO");
                    UpdateStatus("错误: 文件未找到", "#DC3545");
                    return;
                }

                AddLog($"📂 找到执行文件: {System.IO.Path.GetFileName(executablePath)}", "SUCCESS");
                AddLog($"📍 文件路径: {executablePath}", "INFO");
                AddLog($"📊 文件大小: {new System.IO.FileInfo(executablePath).Length / 1024 / 1024:F1} MB", "INFO");
                AddLog("⚡ 正在启动程序...", "INFO");

                // 启动可执行文件
                var processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = executablePath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WorkingDirectory = System.IO.Path.GetDirectoryName(executablePath)
                };

                var stopwatch = Stopwatch.StartNew();

                using (var process = System.Diagnostics.Process.Start(processStartInfo))
                {
                    if (process != null)
                    {
                        AddLog($"🔄 进程已启动 (PID: {process.Id})", "INFO");

                        // 异步读取输出
                        var outputTask = process.StandardOutput.ReadToEndAsync();
                        var errorTask = process.StandardError.ReadToEndAsync();

                        // 等待进程完成
                        await process.WaitForExitAsync();
                        stopwatch.Stop();

                        string output = await outputTask;
                        string error = await errorTask;

                        AddLog($"⏱️ 执行时间: {stopwatch.Elapsed.TotalSeconds:F2} 秒", "INFO");
                        AddLog($"🔚 进程退出代码: {process.ExitCode}", process.ExitCode == 0 ? "SUCCESS" : "ERROR");

                        // 输出详细信息
                        if (!string.IsNullOrEmpty(output))
                        {
                            AddLog("📤 标准输出:", "INFO");
                            foreach (string line in output.Split('\n'))
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                    AddLog($"   {line.Trim()}", "INFO");
                            }
                        }

                        if (!string.IsNullOrEmpty(error))
                        {
                            AddLog("📥 错误输出:", "ERROR");
                            foreach (string line in error.Split('\n'))
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                    AddLog($"   {line.Trim()}", "ERROR");
                            }
                        }

                        if (process.ExitCode == 0)
                        {
                            AddLog("✅ 程序执行成功完成", "SUCCESS");
                            UpdateStatus("执行成功", "#28A745");
                        }
                        else
                        {
                            AddLog($"❌ 程序执行失败 (退出代码: {process.ExitCode})", "ERROR");
                            UpdateStatus($"执行失败 (代码: {process.ExitCode})", "#DC3545");
                        }
                    }
                    else
                    {
                        AddLog("❌ 无法启动进程", "ERROR");
                        UpdateStatus("启动失败", "#DC3545");
                    }
                }
            }
            catch (System.ComponentModel.Win32Exception ex)
            {
                AddLog($"❌ Windows系统错误: {ex.Message}", "ERROR");
                AddLog($"🔍 错误代码: {ex.NativeErrorCode}", "ERROR");
                AddLog("💡 可能的原因: 文件损坏、权限不足或缺少依赖", "INFO");
                UpdateStatus("系统错误", "#DC3545");
            }
            catch (UnauthorizedAccessException ex)
            {
                AddLog($"❌ 权限错误: {ex.Message}", "ERROR");
                AddLog("💡 解决方案: 请以管理员身份运行程序", "INFO");
                UpdateStatus("权限不足", "#DC3545");
            }
            catch (System.IO.FileNotFoundException ex)
            {
                AddLog($"❌ 文件未找到: {ex.Message}", "ERROR");
                AddLog($"🔍 缺失文件: {ex.FileName}", "ERROR");
                UpdateStatus("文件缺失", "#DC3545");
            }
            catch (Exception ex)
            {
                AddLog($"❌ 未知错误: {ex.Message}", "ERROR");
                AddLog($"🔍 错误类型: {ex.GetType().Name}", "ERROR");
                if (ex.InnerException != null)
                {
                    AddLog($"🔍 内部错误: {ex.InnerException.Message}", "ERROR");
                }
                UpdateStatus("执行失败", "#DC3545");
            }
            finally
            {
                _isExecuting = false;
            }
        }

        // 停止执行按钮
        private void StopExecutionButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isExecuting)
            {
                AddLog("🛑 用户请求停止执行", "WARNING");
                UpdateStatus("正在停止...", "#FFC107");
            }
            else
            {
                AddLog("ℹ️ 当前没有正在执行的任务", "INFO");
            }
        }

        // 关于按钮
        private void AboutButton_Click(object sender, RoutedEventArgs e)
        {
            string aboutText = $"Augment Magic v1.0.1\n\n" +
                              $"系统信息: windows-x86_64\n" +
                              $"管理员权限: 是\n\n" +
                              $"这是一个从 PyInstaller 打包文件重构的现代化 WPF 应用程序。\n\n" +
                              $"© 2025 Augment Magic";
            MessageBox.Show(aboutText, "关于 Augment Magic", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // 清除日志按钮
        private void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Text = "";
            AddLog("🗑️ 日志已清空", "INFO");
            UpdateStatus("日志已清空", "#28A745");
        }

        private string[] GetSearchPaths()
        {
            string system = GetSystemName();
            string arch = GetArchitecture();
            string fileName = $"augment-magic-{system}-{arch}";
            if (system == "windows")
            {
                fileName += ".exe";
            }

            return new string[]
            {
                System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "data", fileName),
                System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "AugmentMagic.exe_extracted", "data", fileName),
                System.IO.Path.Combine(Environment.CurrentDirectory, "AugmentMagic.exe_extracted", "data", fileName),
                System.IO.Path.Combine(@"d:\soft\AugmentCode\AugmentMagic.exe_extracted\data", fileName),
                System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "AugmentMagic.exe_extracted", "data", fileName)
            };
        }

        private string GetExecutablePath()
        {
            string[] possiblePaths = GetSearchPaths();

            foreach (string path in possiblePaths)
            {
                if (System.IO.File.Exists(path))
                {
                    return path;
                }
            }

            return null;
        }

        private string GetSystemName()
        {
            var os = Environment.OSVersion.Platform;
            if (os == PlatformID.Win32NT)
                return "windows";
            else if (os == PlatformID.Unix)
                return "linux";
            else if (os == PlatformID.MacOSX)
                return "macos";
            else
                return "windows"; // 默认
        }

        private string GetArchitecture()
        {
            var arch = Environment.Is64BitOperatingSystem ? "x86_64" : "x86";
            return arch;
        }

    }
}
