diff -rupN a/Include/objimpl.h b/Include/objimpl.h
--- a/Include/objimpl.h	2003-04-17 10:29:22.000000000 -0700
+++ b/Include/objimpl.h	2019-09-30 15:42:32.012660303 -0700
@@ -245,6 +245,20 @@ PyAPI_FUNC(PyVarObject *) _PyObject_GC_R
 /* for source compatibility with 2.2 */
 #define _PyObject_GC_Del PyObject_GC_Del
 
+/*
+ * Former over-aligned definition of PyGC_Head, used to compute the size of the
+ * padding for the new version below.
+ */
+union _gc_head;
+union _gc_head_old {
+	struct {
+		union _gc_head_old *gc_next;
+		union _gc_head_old *gc_prev;
+		int gc_refs;
+	} gc;
+	long double dummy;
+};
+
 /* GC information is stored BEFORE the object structure. */
 typedef union _gc_head {
 	struct {
@@ -252,7 +266,8 @@ typedef union _gc_head {
 		union _gc_head *gc_prev;
 		int gc_refs;
 	} gc;
-	long double dummy;  /* force worst-case alignment */
+	double dummy; /* Force at least 8-byte alignment. */
+	char dummy_padding[sizeof(union _gc_head_old)];
 } PyGC_Head;
 
 extern PyGC_Head *_PyGC_generation0;
