using System;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public interface IVersionCheckerService
    {
        Task<bool> CheckForUpdatesAsync();
        Task<VersionInfo?> GetLatestVersionInfoAsync();
        Task<bool> DownloadUpdateAsync(string downloadUrl, string savePath, IProgress<double>? progress = null);
        string GetCurrentVersion();
        bool CompareVersions(string currentVersion, string latestVersion);
    }
}
