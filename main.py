#!/usr/bin/env python3
"""
AugmentMagic - 主程序文件
从 PyInstaller 打包的 .pyc 文件重构而来
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from pathlib import Path
from datetime import datetime
import platform
import ctypes
import os
import sys
import threading
import subprocess
import time

# 尝试导入自定义模块
try:
    from files import *
except ImportError:
    print("Warning: files module not found")

try:
    from TempMailClient import TempMailClient
except ImportError:
    class TempMailClient:
        def __init__(self):
            self.current_email = ""
        
        def get_new_email(self):
            return "<EMAIL>"
        
        def check_inbox(self):
            return []

try:
    from VersionChecker import Version<PERSON>hecker
except ImportError:
    class VersionChecker:
        def __init__(self):
            pass
        
        def check_for_updates(self):
            return False


def get_system_info():
    """获取系统信息，返回操作系统和架构"""
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    # 系统名称映射
    if system == 'darwin':
        system = 'macos'
    elif system == 'windows':
        system = 'windows'
    else:
        system = 'linux'
    
    # 架构映射
    if arch in ['x86_64', 'amd64']:
        arch = 'x86_64'
    elif arch in ['arm64', 'aarch64']:
        arch = 'aarch64'
    
    return system, arch


def get_executable_filename():
    """根据当前系统获取对应的可执行文件名"""
    system, arch = get_system_info()
    filename = f"augment-magic-{system}-{arch}"
    if system == 'windows':
        filename += '.exe'
    return filename


def is_admin():
    """检查当前程序是否以管理员权限运行"""
    try:
        if platform.system() == 'Windows':
            return ctypes.windll.shell32.IsUserAnAdmin()
        else:
            return os.geteuid() == 0
    except:
        return False


def get_resource_path(relative_path):
    """获取资源文件的绝对路径"""
    try:
        # 尝试使用 PyInstaller 的 _MEIPASS
        base_path = sys._MEIPASS
        return os.path.join(base_path, relative_path)
    except AttributeError:
        pass
    
    try:
        # 使用 __file__ 的父目录
        base_path = Path(__file__).parent
        return str(base_path / relative_path)
    except NameError:
        # 使用当前工作目录
        base_path = Path.cwd()
        return str(base_path / relative_path)


class ExecutorApp:
    """主应用程序类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment Magic")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 获取系统信息
        system, arch = get_system_info()
        self.system_display = f"{system}-{arch}"
        
        # 获取可执行文件路径
        executable_filename = get_executable_filename()
        self.executable_path = get_resource_path(f"data/{executable_filename}")
        
        # 初始化邮件客户端
        self.mail_client = TempMailClient()
        self.current_email = ""
        self.is_getting_code = False
        
        # 初始化版本检查器
        self.version_checker = VersionChecker()
        
        # 初始化变量
        self.email_var = tk.StringVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # 设置UI
        self.setup_ui()
        
        # 设置应用图标
        self.set_app_icon()
        
        # 检查权限并显示信息
        self.check_permissions_and_show_info()
        
        # 检查更新
        self.check_for_updates()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Augment Magic", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 系统信息框架
        info_frame = ttk.LabelFrame(main_frame, text="系统信息", padding="5")
        info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)
        
        ttk.Label(info_frame, text="系统:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=self.system_display).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(info_frame, text="管理员权限:").grid(row=1, column=0, sticky=tk.W)
        admin_status = "是" if is_admin() else "否"
        ttk.Label(info_frame, text=admin_status).grid(row=1, column=1, sticky=tk.W)
        
        # 邮箱框架
        email_frame = ttk.LabelFrame(main_frame, text="临时邮箱", padding="5")
        email_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        email_frame.columnconfigure(1, weight=1)
        
        ttk.Label(email_frame, text="邮箱地址:").grid(row=0, column=0, sticky=tk.W)
        email_entry = ttk.Entry(email_frame, textvariable=self.email_var, state="readonly")
        email_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))
        
        ttk.Button(email_frame, text="获取新邮箱", 
                  command=self.get_new_email).grid(row=0, column=2, padx=(5, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.text_area = scrolledtext.ScrolledText(log_frame, 
                                                  wrap=tk.WORD, 
                                                  width=80, 
                                                  height=20)
        self.text_area.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 状态栏
        status_frame = ttk.Frame(button_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=(5, 0))
        
        # 按钮
        ttk.Button(button_frame, text="开始执行", command=self.start_execution).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="停止", command=self.stop_execution).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清除日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT)
    
    def set_app_icon(self):
        """设置应用图标"""
        try:
            # 尝试加载 ICO 图标
            icon_path = get_resource_path("icons/app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
                self.log_message("图标加载成功")
                return
        except Exception:
            pass
        
        try:
            # 尝试加载 PNG 图标
            icon_path = get_resource_path("icons/icon_32.png")
            if os.path.exists(icon_path):
                photo = tk.PhotoImage(file=icon_path)
                self.root.iconphoto(True, photo)
                self.log_message("PNG图标加载成功")
                return
        except Exception as e:
            self.log_message(f"图标加载失败: {e}")
        
        self.log_message("未找到图标文件")
    
    def log_message(self, message):
        """在文本区域记录消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.text_area.insert(tk.END, log_entry)
        self.text_area.see(tk.END)
        self.root.update_idletasks()
    
    def get_new_email(self):
        """获取新的临时邮箱"""
        try:
            self.status_var.set("获取新邮箱...")
            email = self.mail_client.get_new_email()
            self.current_email = email
            self.email_var.set(email)
            self.log_message(f"获取到新邮箱: {email}")
            self.status_var.set("就绪")
        except Exception as e:
            self.log_message(f"获取邮箱失败: {e}")
            self.status_var.set("错误")
    
    def start_execution(self):
        """开始执行主要功能"""
        self.log_message("开始执行...")
        self.status_var.set("执行中...")
        
        # 在新线程中执行，避免阻塞UI
        thread = threading.Thread(target=self._execute_main_task)
        thread.daemon = True
        thread.start()
    
    def _execute_main_task(self):
        """执行主要任务"""
        try:
            if self.executable_path and os.path.exists(self.executable_path):
                self.log_message(f"执行文件: {self.executable_path}")
                
                # 执行可执行文件
                process = subprocess.Popen(
                    [self.executable_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # 读取输出
                stdout, stderr = process.communicate()
                
                if stdout:
                    self.log_message(f"输出: {stdout}")
                if stderr:
                    self.log_message(f"错误: {stderr}")
                
                self.log_message("执行完成")
            else:
                self.log_message("错误: 未找到可执行文件")
            
            self.status_var.set("完成")
        except Exception as e:
            self.log_message(f"执行失败: {e}")
            self.status_var.set("错误")
    
    def stop_execution(self):
        """停止执行"""
        self.log_message("停止执行...")
        self.status_var.set("已停止")
    
    def clear_log(self):
        """清除日志"""
        self.text_area.delete(1.0, tk.END)
    
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""Augment Magic

系统信息: {self.system_display}
可执行文件: {os.path.basename(self.executable_path) if self.executable_path else '未找到'}
管理员权限: {'是' if is_admin() else '否'}

这是一个从 PyInstaller 打包文件重构的应用程序。
原始文件已被反编译并重构为可读的 Python 代码。
"""
        messagebox.showinfo("关于", about_text)
    
    def check_permissions_and_show_info(self):
        """检查权限并显示信息"""
        self.log_message(f"系统信息: {self.system_display}")
        self.log_message(f"管理员权限: {'是' if is_admin() else '否'}")
        if self.executable_path and os.path.exists(self.executable_path):
            self.log_message(f"可执行文件: {self.executable_path}")
        else:
            self.log_message("警告: 未找到可执行文件")
    
    def check_for_updates(self):
        """检查更新"""
        self.log_message("检查更新...")
        try:
            has_update = self.version_checker.check_for_updates()
            if has_update:
                self.log_message("发现新版本")
            else:
                self.log_message("当前版本是最新的")
        except Exception as e:
            self.log_message(f"检查更新失败: {e}")
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = ExecutorApp()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        messagebox.showerror("错误", f"应用程序启动失败: {e}")


if __name__ == "__main__":
    main()
