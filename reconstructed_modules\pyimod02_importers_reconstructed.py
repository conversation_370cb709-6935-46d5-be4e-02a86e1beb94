"""
pyimod02_importers - 从 .pyc 文件重构
"""

# 可能的导入:
# PEP-302 and PEP-451 importers for frozen applications.
# (PyInstaller\loader\pyimod02_importers.py
# https://github.com/python/cpython/blob/3.9/Lib/importlib/_bootstrap_external.py#L679-L688
# ImportError
# importlib.invalidate_caches() when invalidating the caches of all finders on sys.meta_path.
# https://docs.python.org/3/library/importlib.html#importlib.abc.MetaPathFinder.invalidate_caches
# https://docs.python.org/3/library/importlib.html#importlib.abc.PathEntryFinder.find_spec
# _frozen_importlib
# Since this loader is instantiated only from PyiFrozenFinder and since each loader instance is tied to a specific
# A method that returns the module object to use when importing a module. This method may return None, indicating
# https://docs.python.org/3/library/importlib.html#importlib.abc.Loader.create_module
# A method that executes the module in its own namespace when a module is imported or reloaded. The module
# https://docs.python.org/3/library/importlib.html#importlib.abc.Loader.exec_module
# A legacy method for loading a module. If the module cannot be loaded, ImportError is raised, otherwise the
# importlib._bootstrap
# A method that is to return the value of __file__ for the specified module. If no path is available, ImportError
# https://docs.python.org/3/library/importlib.html#importlib.abc.ExecutionLoader.get_filename
# for example, for a built-in module). Raise an ImportError if loader cannot find the requested module.
# https://docs.python.org/3/library/importlib.html#importlib.abc.InspectLoader.get_code
#         module). Raises ImportError if the loader cannot find the module specified.
#         https://docs.python.org/3/library/importlib.html#importlib.abc.InspectLoader.get_source
# A method to return a true value if the module is a package, a false value otherwise. ImportError is raised if
# https://docs.python.org/3/library/importlib.html#importlib.abc.InspectLoader.is_package
# __file__ attribute or an item from a package
# https://docs.python.org/3/library/importlib.html#importlib.abc.ResourceLoader.get_data
# Return resource reader compatible with `importlib.resources`.
# Resource reader for importlib.resources / importlib_resources support.
# file, which might break under some circumstances, e.g., metpy with importlib_resources back-port, due to:
# (importlib_resources tries to use 'fonts/wx_symbols.ttf' as a temporary filename suffix, which fails as it contains
# This makes implementation of mixed support for on-disk and embedded resources using importlib.abc.Traversable
# importlib.readers.FileReader from python 3.10:
#   https://github.com/python/cpython/blob/839d7893943782ee803536a47f1d4de160314f85/Lib/importlib/readers.py#L11
# and its underlying classes, importlib.abc.TraversableResources and importlib.abc.ResourceReader:
#   https://github.com/python/cpython/blob/839d7893943782ee803536a47f1d4de160314f85/Lib/importlib/abc.py#L422
#   https://github.com/python/cpython/blob/839d7893943782ee803536a47f1d4de160314f85/Lib/importlib/abc.py#L312
# Install PyInstaller's frozen finders/loaders/importers into python's import machinery.
# zipimporterz0PyInstaller: inserting our finder hook at index 
#  in sys.path_hooks.zbPyInstaller: zipimporter hook not found in sys.path_hooks! Prepending our finder hook to the list.r
# _patch_zipimporter_get_source
# path_importer_cache
# importlib._bootstrap)
# 2_patch_zipimporter_get_source.<locals>._get_source
# zipimportr

# 可能的类:
class ModuleSpecrP:
    """重构的类"""
    pass

class BytesIO:
    """重构的类"""
    pass

class ValueError:
    """重构的类"""
    pass

class PathrH:
    """重构的类"""
    pass

class Resourcesc:
    """重构的类"""
    pass

class WindowsRegistryFinder:
    """重构的类"""
    pass

class ZlibArchiveReaderr:
    """重构的类"""
    pass

class ImportError:
    """重构的类"""
    pass

class IncrementalNewlineDecoder:
    """重构的类"""
    pass

class RLockr:
    """重构的类"""
    pass

class PyiFrozenLoader:
    """重构的类"""
    pass

class AttributeErrorr:
    """重构的类"""
    pass

class RuntimeErrorrf:
    """重构的类"""
    pass

class PyiFrozenFinder:
    """重构的类"""
    pass

class PyiFrozenEntryPointLoaderij:
    """重构的类"""
    pass

# 可能的函数:
def fallback_specr6():
    """重构的函数"""
    pass

def _orig_get_sources():
    """重构的函数"""
    pass

def top_level_path():
    """重构的函数"""
    pass

def propertyrl():
    """重构的函数"""
    pass

def extract():
    """重构的函数"""
    pass

def __qualname__():
    """重构的函数"""
    pass

def exec():
    """重构的函数"""
    pass

def methods():
    """重构的函数"""
    pass

def enumerate():
    """重构的函数"""
    pass

def is_resource():
    """重构的函数"""
    pass

def fallback_finde():
    """重构的函数"""
    pass

def orignamerP():
    """重构的函数"""
    pass

def _pyi_main_corT():
    """重构的函数"""
    pass

def find_module():
    """重构的函数"""
    pass

def get_codery():
    """重构的函数"""
    pass

def isfile():
    """重构的函数"""
    pass

def rpartitionrV():
    """重构的函数"""
    pass

def portionss():
    """重构的函数"""
    pass

def fullname():
    """重构的函数"""
    pass

def _imp():
    """重构的函数"""
    pass

def delatt():
    """重构的函数"""
    pass

def __doc__rC():
    """重构的函数"""
    pass

def read():
    """重构的函数"""
    pass

def platform():
    """重构的函数"""
    pass

def is_pkg():
    """重构的函数"""
    pass

def _RESOLVED_TOP_LEVEL_DIRECTORY():
    """重构的函数"""
    pass

def flags():
    """重构的函数"""
    pass

def finde():
    """重构的函数"""
    pass

def replacerX():
    """重构的函数"""
    pass

def __main__z():
    """重构的函数"""
    pass

def current():
    """重构的函数"""
    pass

def has_location():
    """重构的函数"""
    pass

def darwinz():
    """重构的函数"""
    pass

def classmethodrK():
    """重构的函数"""
    pass

def tree():
    """重构的函数"""
    pass

def version_info():
    """重构的函数"""
    pass

def install():
    """重构的函数"""
    pass

def open():
    """重构的函数"""
    pass

def __repr__():
    """重构的函数"""
    pass

def stder():
    """重构的函数"""
    pass

def name_components():
    """重构的函数"""
    pass

def __init__():
    """重构的函数"""
    pass

def _build_pyz_prefix_tree():
    """重构的函数"""
    pass

def targets():
    """重构的函数"""
    pass

def __class__():
    """重构的函数"""
    pass

def tokenize():
    """重构的函数"""
    pass

def getrl():
    """重构的函数"""
    pass

def get_data():
    """重构的函数"""
    pass

def pop():
    """重构的函数"""
    pass

def typecode():
    """重构的函数"""
    pass

def verbose():
    """重构的函数"""
    pass

def _TOP_LEVEL_DIRECTORY():
    """重构的函数"""
    pass

def _pyz_entry_prefix():
    """重构的函数"""
    pass

def module_name():
    """重构的函数"""
    pass

def _is_packagerP():
    """重构的函数"""
    pass

def getattrrf():
    """重构的函数"""
    pass

def _pyz_tree():
    """重构的函数"""
    pass

def entryrj():
    """重构的函数"""
    pass

def _decode_source():
    """重构的函数"""
    pass

def startswith():
    """重构的函数"""
    pass

def relative_paths():
    """重构的函数"""
    pass

def source():
    """重构的函数"""
    pass

def _patch_zipimporter_get_source():
    """重构的函数"""
    pass

def __name__():
    """重构的函数"""
    pass

def contents():
    """重构的函数"""
    pass

def _frozen_importlib():
    """重构的函数"""
    pass

def normpath():
    """重构的函数"""
    pass

def selfs():
    """重构的函数"""
    pass

def get_filename():
    """重构的函数"""
    pass

def open_resource():
    """重构的函数"""
    pass

def path_importer_cache():
    """重构的函数"""
    pass

def exec_module():
    """重构的函数"""
    pass

def _MEIPASS():
    """重构的函数"""
    pass

def resource_path():
    """重构的函数"""
    pass

def hasattrre():
    """重构的函数"""
    pass

def joinpath():
    """重构的函数"""
    pass

def _check_name():
    """重构的函数"""
    pass

def split():
    """重构的函数"""
    pass

def relpath():
    """重构的函数"""
    pass

def __path__():
    """重构的函数"""
    pass

def meta_pathrv():
    """重构的函数"""
    pass

def items():
    """重构的函数"""
    pass

def module_files():
    """重构的函数"""
    pass

def path_hooksrK():
    """重构的函数"""
    pass

def resources():
    """重构的函数"""
    pass

def _pyz_tree_lock():
    """重构的函数"""
    pass

def zipimport():
    """重构的函数"""
    pass

def _stdlib_dir():
    """重构的函数"""
    pass

def path_hook():
    """重构的函数"""
    pass

def normcase():
    """重构的函数"""
    pass

def dict():
    """重构的函数"""
    pass

def osrH():
    """重构的函数"""
    pass

def _compute_pyz_entry_name():
    """重构的函数"""
    pass

def get_source():
    """重构的函数"""
    pass

def is_frozen_package():
    """重构的函数"""
    pass

def PYZ_ITEM_NSPKG():
    """重构的函数"""
    pass

def _TOP_LEVEL_DIRECTORY_PATHS():
    """重构的函数"""
    pass

def _get_source():
    """重构的函数"""
    pass

def create_module():
    """重构的函数"""
    pass

def pyz_archive():
    """重构的函数"""
    pass

def entry_data():
    """重构的函数"""
    pass

def bytecodes():
    """重构的函数"""
    pass

def append():
    """重构的函数"""
    pass

def readline():
    """重构的函数"""
    pass

def pyimod01_archive():
    """重构的函数"""
    pass

def pyz_entry_name():
    """重构的函数"""
    pass

def decode():
    """重构的函数"""
    pass

def our_hook_found():
    """重构的函数"""
    pass

def module():
    """重构的函数"""
    pass

def strrH():
    """重构的函数"""
    pass

def trace():
    """重构的函数"""
    pass

def get_resource_reade():
    """重构的函数"""
    pass

def entry_name():
    """重构的函数"""
    pass

def filename():
    """重构的函数"""
    pass

def __module__():
    """重构的函数"""
    pass

def _check_name_wrappe():
    """重构的函数"""
    pass

def _is_macos_app_bundle():
    """重构的函数"""
    pass

def load_module():
    """重构的函数"""
    pass

def removerg():
    """重构的函数"""
    pass

def loader_state():
    """重构的函数"""
    pass

def tail_modules():
    """重构的函数"""
    pass

def source_bytes():
    """重构的函数"""
    pass

def _pyz_archive():
    """重构的函数"""
    pass

def _get_fallback_finderre():
    """重构的函数"""
    pass

def find_loade():
    """重构的函数"""
    pass

def __firstlineno__():
    """重构的函数"""
    pass

def __static_attributes__():
    """重构的函数"""
    pass

def PYZ_ITEM_PKG():
    """重构的函数"""
    pass

def _pyz_entry_name():
    """重构的函数"""
    pass

def _bootstrap():
    """重构的函数"""
    pass

def encoding():
    """重构的函数"""
    pass

def newline_decoders():
    """重构的函数"""
    pass

def hooks():
    """重构的函数"""
    pass

def modules():
    """重构的函数"""
    pass

def is_frozen():
    """重构的函数"""
    pass

def path():
    """重构的函数"""
    pass

def files():
    """重构的函数"""
    pass

def __spec__():
    """重构的函数"""
    pass

def endswithrW():
    """重构的函数"""
    pass

def pathlib():
    """重构的函数"""
    pass

def _find_fallback_spec():
    """重构的函数"""
    pass

def basename():
    """重构的函数"""
    pass

def __loader__():
    """重构的函数"""
    pass

def orig_name():
    """重构的函数"""
    pass

def source_bytes_readline():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'r \\ \\'
#   2: 'r"S'
#   3: 'r&S'
#   4: "r'S"
#   5: 'r(S'
#   6: 'r)g'
#   7: 'PEP-302 and PEP-451 importers for frozen applications.'
#   8: 'sys'
#   9: 'stderr'
#  10: 'write)'
#  11: 'msg'
#  12: '(PyInstaller\\loader\\pyimod02_importers.py'
#  13: 'tracer'
#  14: '  r'
#  16: "Based on CPython's implementation of the same functionality:"
#  17: 'https://github.com/python/cpython/blob/3.9/Lib/importlib/_bootstrap_external.py#L679-L688'
#  18: 'detect_encodingNT)'
#  19: 'decoder'
#  20: 'translate)'
#  21: 'tokenizer'
#  22: 'BytesIO'
#  23: 'readline'
#  24: 'IncrementalNewlineDecoder'
#  25: 'decode)'
#  26: 'source_bytesr'
#  27: 'source_bytes_readline'
#  28: 'encoding'
#  29: 'newline_decoders'
#  30: '     r'
#  31: '_decode_sourcer'
#  32: '_pyz_tree_lock'
#  33: '_pyz_tree'
#  34: '_build_pyz_prefix_tree'
#  35: 'pyz_archiver'
#  36: 'get_pyz_toc_treer#'
#  37: 'darwinz'
#  38: 'Contents/FrameworksT'
#  39: 'Resourcesc'
#  40: 'p#U'
#  41: 'XdS'
#  42: 'dict'
#  43: 'toc'
#  44: 'items'
#  45: 'split'
#  46: 'pyimod01_archive'
#  47: 'PYZ_ITEM_PKG'
#  48: 'PYZ_ITEM_NSPKG'
#  49: 'setdefault)'
#  50: 'tree'
#  51: 'entry_name'
#  52: 'entry_data'
#  53: 'name_components'
#  54: 'typecode'
#  55: 'current'
#  56: 'name_components'
#  57: '        r'
#  58: 'PyiFrozenFinder'
#  59: "PyInstaller's frozen path entry finder for specific search path."
#  63: '`myotherpacakge` added this path to `sys.path`), then "mypackage.mod" would need to translate to'
#  64: '"myotherpackage._vendored.mypackage.mod" in the PYZ archive.'
#  65: '__class__'
#  66: '__name__'
#  67: '_path)'
#  68: 'selfs'
#  69: '__repr__'
#  70: 'PyiFrozenFinder.__repr__'
#  71: 'Nz0PyInstaller: running path finder hook for path: z'
#  72: 'PyInstaller: hook succeededz'
#  73: 'PyInstaller: hook failed: )'
#  74: 'Exception)'
#  75: 'cls'
#  76: 'path'
#  77: 'finder'
#  78: '    r'
#  79: 'path_hook'
#  80: 'PyiFrozenFinder.path_hook'
#  81: '..zIFailed to determine relative path w.r.t. top-level application directory.z'
#  82: "only directories are supportedr'"
#  83: '_pyz_archive'
#  84: '_TOP_LEVEL_DIRECTORY_PATHS'
#  85: 'osrH'
#  86: 'relpath'
#  87: 'ValueError'
#  88: 'startswith'
#  89: 'ImportError'
#  90: 'isfile'
#  91: '_pyz_entry_prefix'
#  92: 'joinr-'
#  93: 'sep)'
#  94: 'top_level_path'
#  95: 'relative_paths'
#  96: '    r'
#  97: '__init__'
#  98: 'PyiFrozenFinder.__init__'
# 100: 'rpartitionrV'
# 101: 'fullname'
# 102: 'tail_modules'
# 103: '   r'
# 104: '_compute_pyz_entry_name'
# 105: "'PyiFrozenFinder._compute_pyz_entry_name"
# 106: 'p#X0R'
# 109: 'that comes after ours is eligible to be a fallback.'
# 112: 'without subclassing FileFinder.'
# 113: '_fallback_finderFNT)'
# 114: 'hasattrre'
# 115: 'enumerater'
# 116: 'path_hooksrK'
# 117: 'our_hook_found'
# 118: 'idx'
# 119: 'hooks'
# 120: '    r'
# 121: 'fallback_finder'
# 122: 'PyiFrozenFinder.fallback_finder'
# 125: 'modules that are collected only as source .py files.'
# 128: 'without subclassing FileFinder.'
# 129: '_get_fallback_finderre'
# 130: 'find_spec)'
# 131: 'targets'
# 132: '   r'
# 133: '_find_fallback_spec'
# 134: '#PyiFrozenFinder._find_fallback_spec'
# 135: 'A method which, when called, should invalidate any internal cache used by the finder. Used by'
# 136: 'importlib.invalidate_caches() when invalidating the caches of all finders on sys.meta_path.'
# 137: 'https://docs.python.org/3/library/importlib.html#importlib.abc.MetaPathFinder.invalidate_caches'
# 138: 'invalidate_caches)'
# 139: 'getattrrf'
# 140: '  r'
# 141: '!PyiFrozenFinder.invalidate_caches'
# 144: 'module object that the finder may use to make a more educated guess about what spec to return.'
# 145: 'https://docs.python.org/3/library/importlib.html#importlib.abc.PathEntryFinder.find_spec'
# 146: 'z": find_spec: called with fullname=z'
# 147: ', target=Nz'
# 148: ': find_spec: z'
# 149: " not found in PYZ...z6: find_spec: attempting resolve using fallback finder r'"
# 150: 'z,: find_spec: fallback finder returned spec: z.: find_spec: fallback finder is not available.r'
# 151: ': find_spec: found z'
# 152: ' in PYZ as z'
# 153: ', typecode=)'
# 154: 'namer"'
# 155: 'pyz_entry_name'
# 156: 'is_package)'
# 157: 'originT)'
# 158: 'getrl'
# 159: '_frozen_importlib'
# 160: 'ModuleSpecrP'
# 161: '_MEIPASS'
# 162: 'replacerX'
# 163: 'submodule_search_locationsr/'
# 164: 'PyiFrozenLoader'
# 165: 'has_location'
# 166: 'dirname)'
# 167: 'fallback_specr6'
# 168: 'specr{'
# 169: 'loaderr|'
# 170: '           r'
# 171: 'PyiFrozenFinder.find_spec'
# 177: '(i.e. failure to find anything for the module).'
# 178: 'Deprecated since python 3.4, removed in 3.12.'
# 179: '   r'
# 180: 'find_loader'
# 181: 'PyiFrozenFinder.find_loaderZ'
# 182: 'p#U'
# 184: 'Deprecated since python 3.4, removed in 3.12.'
# 185: 'portionss'
# 186: '    r'
# 187: 'find_module'
# 188: 'PyiFrozenFinder.find_modulel'
# 189: '__module__'
# 190: '__qualname__'
# 191: '__firstlineno__'
# 192: '__doc__rC'
# 193: 'classmethodrK'
# 194: 'propertyrl'
# 195: 'version_infor'
# 196: '__static_attributes__r'
# 197: 'loader for z'
# 198: ' cannot handle '
# 199: 'args'
# 200: 'kwargs'
# 201: 'methods'
# 202: '_check_name_wrapper'
# 203: '(_check_name.<locals>._check_name_wrapper|'
# 204: '` r'
# 205: '_check_namer'
# 206: "PyInstaller's frozen loader for modules in the PYZ archive, which are discovered by PyiFrozenFinder."
# 209: "Hence, we can avoid any additional validation in the implementation of the loader's methods."
# 210: 'X l'
# 211: 'X0l'
# 212: 'X@l'
# 213: 'OY['
# 214: 'XPl'
# 215: "Nr'"
# 216: '__init__.py'
# 217: '.py)'
# 218: '_pyz_entry_name'
# 219: '_is_packagerP'
# 220: 'module_files'
# 221: '      r'
# 222: 'PyiFrozenLoader.__init__'
# 224: 'that default module creation semantics should take place.'
# 225: 'https://docs.python.org/3/library/importlib.html#importlib.abc.Loader.create_module'
# 226: '  r'
# 227: 'create_module'
# 228: 'PyiFrozenLoader.create_module'
# 229: 'X1R'
# 231: 'should already be initialized when exec_module() is called. When this method exists, create_module()'
# 232: 'must be defined.'
# 233: 'https://docs.python.org/3/library/importlib.html#importlib.abc.Loader.exec_module'
# 234: 'Nz Failed to retrieve bytecode for '
# 235: '__file__)'
# 236: '__spec__'
# 237: 'get_codery'
# 238: 'RuntimeErrorrf'
# 239: '__path__'
# 240: 'exec'
# 241: '__dict__)'
# 242: 'moduler'
# 243: 'bytecodes'
# 244: '    r'
# 245: 'exec_module'
# 246: 'PyiFrozenLoader.exec_module'
# 248: 'loaded module is returned.'
# 250: 'both v3.12.4 and v3.13.0rc1).'
# 251: 'importlib._bootstrap'
# 252: '_bootstrap'
# 253: '_load_module_shim)'
# 254: '   r'
# 255: 'load_module'
# 256: 'PyiFrozenLoader.load_module'
# 258: 'is raised.'
# 260: 'bytecode was used to load the module.'
# 261: 'https://docs.python.org/3/library/importlib.html#importlib.abc.ExecutionLoader.get_filename'
# 262: '  r'
# 263: 'get_filename'
# 264: 'PyiFrozenLoader.get_filename'
# 267: 'https://docs.python.org/3/library/importlib.html#importlib.abc.InspectLoader.get_code'
# 268: 'extractr'
# 269: '  r'
# 270: 'PyiFrozenLoader.get_code'
# 272: "        all recognized line separators into '"
# 273: "' characters. Returns None if no source is available (e.g. a built-in"
# 274: '        module). Raises ImportError if the loader cannot find the module specified.'
# 275: '        https://docs.python.org/3/library/importlib.html#importlib.abc.InspectLoader.get_source'
# 276: 'rbN)'
# 277: 'open'
# 278: 'readr'
# 279: 'FileNotFoundError)'
# 280: 'filename'
# 281: 'fpr'
# 282: '     r'
# 283: 'get_source'
# 284: 'PyiFrozenLoader.get_source'
# 286: 'the loader cannot find the module.'
# 287: 'https://docs.python.org/3/library/importlib.html#importlib.abc.InspectLoader.is_package'
# 288: '  r'
# 289: 'PyiFrozenLoader.is_package'
# 293: '__file__ attribute or an item from a package'
# 294: 's __path__.'
# 295: 'https://docs.python.org/3/library/importlib.html#importlib.abc.ResourceLoader.get_data'
# 296: '   r'
# 297: 'get_data'
# 298: 'PyiFrozenLoader.get_data'
# 299: 'Return resource reader compatible with `importlib.resources`.'
# 300: 'PyiFrozenResourceReader)'
# 301: '  r'
# 302: 'get_resource_reader'
# 303: '#PyiFrozenLoader.get_resource_reader,'
# 304: 'Resource reader for importlib.resources / importlib_resources support.'
# 307: 'to contain only .pyc modules.'
# 311: 'anyway, which will place them on filesystem and make them appear as resources.'
# 316: 'a separator).'
# 317: 'Furthermore, some packages expect files() to return either pathlib.Path or zipfile.Path, e.g.,'
# 320: 'protocol rather difficult.'
# 322: 'importlib.readers.FileReader from python 3.10:'
# 324: 'and its underlying classes, importlib.abc.TraversableResources and importlib.abc.ResourceReader:'
# 327: 'pathlib'
# 328: 'PathrH'
# 329: 'parent)'
# 330: '   r'
# 331: ' PyiFrozenResourceReader.__init__S'
# 332: 'files'
# 333: 'joinpathr'
# 334: 'resources'
# 335: '  r'
# 336: 'open_resource'
# 337: '%PyiFrozenResourceReader.open_resourceZ'
# 338: 'strrH'
# 339: '  r'
# 340: 'resource_path'
# 341: '%PyiFrozenResourceReader.resource_path]'
# 342: 'is_file)'
# 343: '  r'
# 344: 'is_resource'
# 345: '#PyiFrozenResourceReader.is_resource`'
# 346: 'items'
# 347: '  r'
# 348: '<genexpr>'
# 349: '3PyiFrozenResourceReader.contents.<locals>.<genexpr>d'
# 350: 'iterdir)'
# 351: 'contents'
# 352: ' PyiFrozenResourceReader.contentsc'
# 353: 'PyiFrozenResourceReader.filesf'
# 354: 'PyiFrozenEntryPointLoaderij'
# 355: 'A special loader that enables retrieval of the code-object for the __main__ module.'
# 356: '"PyiFrozenEntryPointLoader.__repr__n'
# 357: '__main__z'
# 358: ' cannot handle module )'
# 359: 'modules'
# 360: '_pyi_main_corT'
# 361: '  r'
# 362: '"PyiFrozenEntryPointLoader.get_codeq'
# 363: 'p!['
# 364: 'NJf'
# 365: "Install PyInstaller's frozen finders/loaders/importers into python's import machinery."
# 366: '_pyinstaller_pyzz,Bootloader did not set sys._pyinstaller_pyz!T)'
# 367: 'check_pymagicz#Failed to setup PYZ archive reader!Nr@'
# 368: 'WindowsRegistryFinder'
# 369: 'zipimporterz0PyInstaller: inserting our finder hook at index '
# 371: 'ZlibArchiveReaderr'
# 372: 'delattr'
# 373: 'meta_pathrv'
# 374: 'removerg'
# 375: 'insertr:'
# 376: '_patch_zipimporter_get_source'
# 377: 'path_importer_cache'
# 378: 'popr'
# 379: '__loader__r'
# 380: '_fixup_frozen_stdlib)'
# 381: 'entryrj'
# 382: '   r'
# 383: 'installr'
# 384: ' G&'
# 385: 'Xbl'
# 386: 'Xdl'
# 387: 'N?f'
# 388: ".__init__r'"
# 389: '.pycr'
# 390: 'importlib._bootstrap)'
# 391: '_impr'
# 392: '_stdlib_dirr'
# 393: 'AttributeErrorr'
# 394: 'is_frozen'
# 395: 'is_frozen_packager'
# 396: 'loader_state'
# 397: 'orignamerP'
# 398: 'module_namer'
# 399: 'is_pkgr'
# 400: 'orig_namer'
# 401: '       r'
# 402: 'O/['
# 403: "base_library.zipr'"
# 404: 'basename'
# 405: 'archiver{'
# 406: '_RESOLVED_TOP_LEVEL_DIRECTORYr'
# 407: 'sourcer'
# 408: '_orig_get_sources'
# 409: '_get_source'
# 410: '2_patch_zipimporter_get_source.<locals>._get_source'
# 411: 'zipimportr'
# 412: '  @r'
# 413: ')*r'
# 414: '_threadr.'
# 415: 'flags'
# 416: 'verboser'
# 417: 'RLockr'
# 418: 'normpathr'
# 419: '_TOP_LEVEL_DIRECTORY'
# 420: 'append'
# 421: 'realpathr%'
# 422: 'normcase'
# 423: '_is_macos_app_bundle'
# 424: 'platform'
# 425: 'endswithrW'
# 426: ' _ALTERNATIVE_TOP_LEVEL_DIRECTORY'
# 427: ')_RESOLVED_ALTERNATIVE_TOP_LEVEL_DIRECTORYr!'
# 428: '<module>r9'