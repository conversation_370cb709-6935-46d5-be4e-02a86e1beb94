"""
pyi_rth_multiprocessing - 从 .pyc 文件重构
"""

# 可能的导入:
# _args_from_interpreter_flagsc
# z1from multiprocessing.resource_tracker import mainz+from multiprocessing.forkserver import main

# 可能的函数:
def subprocess():
    """重构的函数"""
    pass

def syss():
    """重构的函数"""
    pass

def process():
    """重构的函数"""
    pass

def freeze_support():
    """重构的函数"""
    pass

def startswith():
    """重构的函数"""
    pass

def multiprocessing():
    """重构的函数"""
    pass

def _freeze_support():
    """重构的函数"""
    pass

def ORIGINAL_DIR():
    """重构的函数"""
    pass

def exit():
    """重构的函数"""
    pass

def split():
    """重构的函数"""
    pass

def kwds():
    """重构的函数"""
    pass

def exec():
    """重构的函数"""
    pass

def argv():
    """重构的函数"""
    pass

def is_forking():
    """重构的函数"""
    pass

def name():
    """重构的函数"""
    pass

def value():
    """重构的函数"""
    pass

def spawn():
    """重构的函数"""
    pass

def _args_from_interpreter_flagsc():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: '_args_from_interpreter_flagsc'
#   2: 'p#U'
#   3: 'z1from multiprocessing.resource_tracker import mainz+from multiprocessing.forkserver import main'
#   4: 'None'
#   5: 'len'
#   6: 'argv'
#   7: 'startswith'
#   8: 'set'
#   9: 'exec'
#  10: 'exit'
#  11: 'spawn'
#  12: 'is_forking'
#  13: 'split'
#  14: 'int'
#  15: 'spawn_main)'
#  16: 'kwds'
#  17: 'arg'
#  18: 'name'
#  19: 'valuer'
#  20: 'multiprocessing'
#  21: 'syss'
#  22: '4PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py'
#  23: '_freeze_support'
#  24: '$_pyi_rthook.<locals>._freeze_support'
#  25: 'multiprocessing.spawn'
#  26: 'subprocessr'
#  27: 'process'
#  28: 'ORIGINAL_DIR'
#  29: 'freeze_supportr'
#  30: ' @@@r'
#  31: "_pyi_rthookr'"
#  32: '%Ir!'
#  33: '<module>r('