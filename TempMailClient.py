#!/usr/bin/env python3
"""
TempMailClient - 临时邮箱客户端
从 .pyc 文件分析重构而来
"""

import requests
import time
import random
import string
from datetime import datetime


class TempMailClient:
    """临时邮箱客户端类"""
    
    def __init__(self):
        self.current_email = ""
        self.session = requests.Session()
        self.base_url = "https://api.tempmail.org"
        
    def generate_random_email(self):
        """生成随机邮箱地址"""
        # 生成随机用户名
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        
        # 常用的临时邮箱域名
        domains = [
            "tempmail.org",
            "10minutemail.com", 
            "guerrillamail.com",
            "mailinator.com",
            "temp-mail.org"
        ]
        
        domain = random.choice(domains)
        return f"{username}@{domain}"
    
    def get_new_email(self):
        """获取新的临时邮箱地址"""
        try:
            # 尝试从API获取
            response = self.session.get(f"{self.base_url}/request/mail/id/")
            if response.status_code == 200:
                data = response.json()
                if 'mail' in data:
                    self.current_email = data['mail']
                    return self.current_email
        except Exception as e:
            print(f"API获取邮箱失败: {e}")
        
        # 如果API失败，生成随机邮箱
        self.current_email = self.generate_random_email()
        return self.current_email
    
    def check_inbox(self, email=None):
        """检查收件箱"""
        if not email:
            email = self.current_email
        
        if not email:
            return []
        
        try:
            # 尝试从API获取邮件
            response = self.session.get(f"{self.base_url}/request/mail/id/{email}/")
            if response.status_code == 200:
                data = response.json()
                return data.get('list', [])
        except Exception as e:
            print(f"检查收件箱失败: {e}")
        
        return []
    
    def get_email_content(self, email_id):
        """获取邮件内容"""
        try:
            response = self.session.get(f"{self.base_url}/request/one_mail/id/{email_id}/")
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            print(f"获取邮件内容失败: {e}")
        
        return None
    
    def wait_for_email(self, timeout=300, check_interval=10):
        """等待邮件到达"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            emails = self.check_inbox()
            if emails:
                return emails
            
            time.sleep(check_interval)
        
        return []
    
    def extract_verification_code(self, email_content):
        """从邮件内容中提取验证码"""
        import re
        
        if not email_content:
            return None
        
        # 常见的验证码模式
        patterns = [
            r'验证码[：:]\s*(\d{4,8})',
            r'verification code[：:]\s*(\d{4,8})',
            r'code[：:]\s*(\d{4,8})',
            r'(\d{6})',  # 6位数字
            r'(\d{4})',  # 4位数字
        ]
        
        text = str(email_content)
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def get_current_email(self):
        """获取当前邮箱地址"""
        return self.current_email
    
    def set_email(self, email):
        """设置邮箱地址"""
        self.current_email = email


# 测试代码
if __name__ == "__main__":
    client = TempMailClient()
    
    # 获取新邮箱
    email = client.get_new_email()
    print(f"新邮箱: {email}")
    
    # 检查收件箱
    emails = client.check_inbox()
    print(f"收件箱邮件数量: {len(emails)}")
    
    if emails:
        for email_item in emails:
            print(f"邮件: {email_item}")
            
            # 获取邮件内容
            content = client.get_email_content(email_item.get('id', ''))
            if content:
                # 尝试提取验证码
                code = client.extract_verification_code(content)
                if code:
                    print(f"找到验证码: {code}")
