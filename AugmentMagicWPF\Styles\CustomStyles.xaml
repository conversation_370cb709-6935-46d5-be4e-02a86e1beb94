<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Cyber Button Style -->
    <Style x:Key="CyberButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource CyberGradientBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#00D4FF"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#00D4FF" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#00D4FF" BlurRadius="15" ShadowDepth="0" Opacity="0.8"/>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="BorderBrush" Value="#00FFFF"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#0099CC"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Neon Card Style -->
    <Style x:Key="NeonCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#333333"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" BlurRadius="20" ShadowDepth="5" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Glowing Text Style -->
    <Style x:Key="GlowingTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="#00D4FF"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#00D4FF" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Tech Input Style -->
    <Style x:Key="TechInputStyle" TargetType="TextBox">
        <Setter Property="Background" Value="#1A1A1A"/>
        <Setter Property="Foreground" Value="#00D4FF"/>
        <Setter Property="BorderBrush" Value="#333333"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="#00D4FF"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#00D4FF" BlurRadius="5" ShadowDepth="0" Opacity="0.4"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Animated Progress Bar -->
    <Style x:Key="CyberProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="6"/>
        <Setter Property="Background" Value="#333333"/>
        <Setter Property="Foreground" Value="{StaticResource CyberGradientBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}" CornerRadius="3">
                        <Border Name="PART_Track" Background="{TemplateBinding Foreground}"
                               HorizontalAlignment="Left" CornerRadius="3">
                            <Border.Effect>
                                <DropShadowEffect Color="#00D4FF" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                            </Border.Effect>
                        </Border>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Status Indicator Style -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Fill" Value="#4CAF50"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#4CAF50" BlurRadius="6" ShadowDepth="0" Opacity="0.8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Cyber ScrollBar Style -->
    <Style x:Key="CyberScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Background" Value="#1A1A1A"/>
        <Setter Property="Foreground" Value="#00D4FF"/>
        <Setter Property="Width" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid>
                        <Border Background="{TemplateBinding Background}" CornerRadius="6"/>
                        <Track Name="PART_Track" IsDirectionReversed="True">
                            <Track.Thumb>
                                <Thumb>
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Background="{TemplateBinding Foreground}" 
                                                  CornerRadius="6" Opacity="0.7"/>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Animated Icon Style -->
    <Style x:Key="AnimatedIconStyle" TargetType="materialDesign:PackIcon">
        <Setter Property="Foreground" Value="#00D4FF"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#00D4FF" BlurRadius="5" ShadowDepth="0" Opacity="0.5"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                           To="360" Duration="0:0:1" RepeatBehavior="Forever"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
