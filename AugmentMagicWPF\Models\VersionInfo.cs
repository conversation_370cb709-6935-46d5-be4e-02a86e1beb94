using System;
using System.Collections.Generic;

namespace AugmentMagicWPF.Models
{
    public class VersionInfo
    {
        public string Version { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime PublishedAt { get; set; }
        public string DownloadUrl { get; set; } = string.Empty;
        public List<VersionAsset> Assets { get; set; } = new List<VersionAsset>();
        public bool IsPrerelease { get; set; }
        public string ReleaseNotes { get; set; } = string.Empty;
    }

    public class VersionAsset
    {
        public string Name { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public long Size { get; set; }
        public string ContentType { get; set; } = string.Empty;
    }
}
