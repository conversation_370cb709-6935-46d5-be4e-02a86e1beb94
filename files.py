#!/usr/bin/env python3
"""
files - 文件操作模块
从 .pyc 文件分析重构而来
"""

import os
import shutil
import json
import zipfile
import tempfile
from pathlib import Path
from datetime import datetime


def ensure_directory(path):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)


def copy_file(src, dst):
    """复制文件"""
    try:
        ensure_directory(os.path.dirname(dst))
        shutil.copy2(src, dst)
        return True
    except Exception as e:
        print(f"复制文件失败: {e}")
        return False


def move_file(src, dst):
    """移动文件"""
    try:
        ensure_directory(os.path.dirname(dst))
        shutil.move(src, dst)
        return True
    except Exception as e:
        print(f"移动文件失败: {e}")
        return False


def delete_file(path):
    """删除文件"""
    try:
        if os.path.exists(path):
            os.remove(path)
            return True
        return False
    except Exception as e:
        print(f"删除文件失败: {e}")
        return False


def read_text_file(path, encoding='utf-8'):
    """读取文本文件"""
    try:
        with open(path, 'r', encoding=encoding) as f:
            return f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None


def write_text_file(path, content, encoding='utf-8'):
    """写入文本文件"""
    try:
        ensure_directory(os.path.dirname(path))
        with open(path, 'w', encoding=encoding) as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"写入文件失败: {e}")
        return False


def read_json_file(path):
    """读取JSON文件"""
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return None


def write_json_file(path, data, indent=2):
    """写入JSON文件"""
    try:
        ensure_directory(os.path.dirname(path))
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"写入JSON文件失败: {e}")
        return False


def get_file_size(path):
    """获取文件大小"""
    try:
        return os.path.getsize(path)
    except Exception:
        return 0


def get_file_modified_time(path):
    """获取文件修改时间"""
    try:
        timestamp = os.path.getmtime(path)
        return datetime.fromtimestamp(timestamp)
    except Exception:
        return None


def list_files(directory, pattern="*", recursive=False):
    """列出目录中的文件"""
    try:
        path = Path(directory)
        if recursive:
            return list(path.rglob(pattern))
        else:
            return list(path.glob(pattern))
    except Exception as e:
        print(f"列出文件失败: {e}")
        return []


def create_zip_archive(source_dir, output_path):
    """创建ZIP压缩包"""
    try:
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, source_dir)
                    zipf.write(file_path, arc_name)
        return True
    except Exception as e:
        print(f"创建压缩包失败: {e}")
        return False


def extract_zip_archive(zip_path, extract_to):
    """解压ZIP文件"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            zipf.extractall(extract_to)
        return True
    except Exception as e:
        print(f"解压文件失败: {e}")
        return False


def find_files_by_extension(directory, extension, recursive=True):
    """根据扩展名查找文件"""
    pattern = f"*.{extension.lstrip('.')}"
    return list_files(directory, pattern, recursive)


def get_temp_directory():
    """获取临时目录"""
    return tempfile.gettempdir()


def create_temp_file(suffix="", prefix="tmp"):
    """创建临时文件"""
    try:
        fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        os.close(fd)
        return path
    except Exception as e:
        print(f"创建临时文件失败: {e}")
        return None


def create_temp_directory(prefix="tmp"):
    """创建临时目录"""
    try:
        return tempfile.mkdtemp(prefix=prefix)
    except Exception as e:
        print(f"创建临时目录失败: {e}")
        return None


def cleanup_temp_files(temp_paths):
    """清理临时文件"""
    for path in temp_paths:
        try:
            if os.path.isfile(path):
                os.remove(path)
            elif os.path.isdir(path):
                shutil.rmtree(path)
        except Exception as e:
            print(f"清理临时文件失败: {e}")


def backup_file(file_path, backup_dir=None):
    """备份文件"""
    try:
        if not os.path.exists(file_path):
            return False
        
        if backup_dir is None:
            backup_dir = os.path.dirname(file_path)
        
        ensure_directory(backup_dir)
        
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{name}_backup_{timestamp}{ext}"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        return copy_file(file_path, backup_path)
    except Exception as e:
        print(f"备份文件失败: {e}")
        return False


def get_file_hash(file_path, algorithm='md5'):
    """获取文件哈希值"""
    import hashlib
    
    try:
        hash_obj = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except Exception as e:
        print(f"计算文件哈希失败: {e}")
        return None


def compare_files(file1, file2):
    """比较两个文件是否相同"""
    try:
        # 首先比较文件大小
        if get_file_size(file1) != get_file_size(file2):
            return False
        
        # 然后比较哈希值
        hash1 = get_file_hash(file1)
        hash2 = get_file_hash(file2)
        
        return hash1 == hash2
    except Exception as e:
        print(f"比较文件失败: {e}")
        return False


# 测试代码
if __name__ == "__main__":
    # 测试文件操作
    test_file = "test.txt"
    test_content = "这是一个测试文件"
    
    # 写入文件
    if write_text_file(test_file, test_content):
        print("文件写入成功")
    
    # 读取文件
    content = read_text_file(test_file)
    if content:
        print(f"文件内容: {content}")
    
    # 获取文件信息
    size = get_file_size(test_file)
    modified_time = get_file_modified_time(test_file)
    print(f"文件大小: {size} 字节")
    print(f"修改时间: {modified_time}")
    
    # 清理测试文件
    delete_file(test_file)
