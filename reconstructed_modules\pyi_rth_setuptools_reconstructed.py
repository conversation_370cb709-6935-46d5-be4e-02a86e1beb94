"""
pyi_rth_setuptools - 从 .pyc 文件重构
"""

# 可能的函数:
def _distutils_hack():
    """重构的函数"""
    pass

def __version__():
    """重构的函数"""
    pass

def stdlib():
    """重构的函数"""
    pass

def environ():
    """重构的函数"""
    pass

def setuptools_majo():
    """重构的函数"""
    pass

def local():
    """重构的函数"""
    pass

def default_value():
    """重构的函数"""
    pass

def _pyi_rthook():
    """重构的函数"""
    pass

def split():
    """重构的函数"""
    pass

def setuptools():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'stdlib'
#   2: 'local'
#   3: 'SETUPTOOLS_USE_DISTUTILS)'
#   4: 'setuptools'
#   5: 'int'
#   6: '__version__'
#   7: 'split'
#   8: 'environ'
#   9: 'get'
#  10: '_distutils_hack'
#  11: 'add_shim)'
#  12: 'setuptools_major'
#  13: 'default_valuer'
#  14: '/PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py'
#  15: '"_install_setuptools_distutils_hack'
#  16: '7_pyi_rthook.<locals>._install_setuptools_distutils_hack'
#  17: 'Exception)'
#  18: '_pyi_rthookr'
#  19: '<module>r'