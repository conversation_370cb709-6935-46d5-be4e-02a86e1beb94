"""
pyi_rth__tkinter - 从 .pyc 文件重构
"""

# 可能的函数:
def path():
    """重构的函数"""
    pass

def is_darwins():
    """重构的函数"""
    pass

def environ():
    """重构的函数"""
    pass

def darwin():
    """重构的函数"""
    pass

def _tcl_data():
    """重构的函数"""
    pass

def _MEIPASS():
    """重构的函数"""
    pass

def tcldi():
    """重构的函数"""
    pass

def _pyi_rthook():
    """重构的函数"""
    pass

def tkdi():
    """重构的函数"""
    pass

def _tk_data():
    """重构的函数"""
    pass

def join():
    """重构的函数"""
    pass

def platform():
    """重构的函数"""
    pass

def isdi():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'X R'
#   2: 'X0R'
#   3: '_tcl_data'
#   4: '_tk_data'
#   5: 'darwin'
#   6: 'TCL_LIBRARYz"Tcl data directory "%s" not found.'
#   7: 'TK_LIBRARYz!Tk data directory "%s" not found.)'
#   8: 'sys'
#   9: 'path'
#  10: 'join'
#  11: '_MEIPASS'
#  12: 'platform'
#  13: 'isdir'
#  14: 'environ'
#  15: 'FileNotFoundError)'
#  16: 'tcldir'
#  17: 'tkdir'
#  18: 'is_darwins'
#  19: '-PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py'
#  20: '_pyi_rthookr'
#  21: '<module>r'