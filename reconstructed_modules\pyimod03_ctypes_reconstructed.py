"""
pyimod03_ctypes - 从 .pyc 文件重构
"""

# 可能的导入:
# This must be done from a function as opposed to at module-level, because when the module is imported/executed,
# the import machinery is not completely set up yet.
# 'install.<locals>.PyInstallerImportError
# 0install.<locals>.PyInstallerImportError.__init__*
# PyInstallerImportErrorr
# ImportError

# 可能的类:
class PyInstallerOleDLLrC:
    """重构的类"""
    pass

class PyInstallerPyDLLr4:
    """重构的类"""
    pass

class CDLL:
    """重构的类"""
    pass

class PATHz:
    """重构的类"""
    pass

class PyInstallerImportErrorr:
    """重构的类"""
    pass

class LibraryLoader:
    """重构的类"""
    pass

class ImportError:
    """重构的类"""
    pass

class PyDLL:
    """重构的类"""
    pass

class OSError:
    """重构的类"""
    pass

class OleDLL:
    """重构的类"""
    pass

class WinDLL:
    """重构的类"""
    pass

# 可能的函数:
def cdll():
    """重构的函数"""
    pass

def _MEIPASS():
    """重构的函数"""
    pass

def _frozen_name():
    """重构的函数"""
    pass

def __qualname__():
    """重构的函数"""
    pass

def installrh():
    """重构的函数"""
    pass

def __class__():
    """重构的函数"""
    pass

def split():
    """重构的函数"""
    pass

def join():
    """重构的函数"""
    pass

def windll():
    """重构的函数"""
    pass

def isfile():
    """重构的函数"""
    pass

def find_msvcrt():
    """重构的函数"""
    pass

def startswith():
    """重构的函数"""
    pass

def ctypes():
    """重构的函数"""
    pass

def __module__():
    """重构的函数"""
    pass

def pyinstaller_find_library():
    """重构的函数"""
    pass

def util():
    """重构的函数"""
    pass

def search_dirs():
    """重构的函数"""
    pass

def name():
    """重构的函数"""
    pass

def __firstlineno__():
    """重构的函数"""
    pass

def platform():
    """重构的函数"""
    pass

def pathsep():
    """重构的函数"""
    pass

def lowe():
    """重构的函数"""
    pass

def frozen_name():
    """重构的函数"""
    pass

def super():
    """重构的函数"""
    pass

def DEFAULT_LIBRARY_FALLBACK():
    """重构的函数"""
    pass

def __name__():
    """重构的函数"""
    pass

def path():
    """重构的函数"""
    pass

def environ():
    """重构的函数"""
    pass

def __doc__():
    """重构的函数"""
    pass

def insert():
    """重构的函数"""
    pass

def fname():
    """重构的函数"""
    pass

def __init__():
    """重构的函数"""
    pass

def pydll():
    """重构的函数"""
    pass

def directory():
    """重构的函数"""
    pass

def oledll():
    """重构的函数"""
    pass

def winc():
    """重构的函数"""
    pass

def self():
    """重构的函数"""
    pass

def __static_attributes__():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'Hooks to make ctypes.CDLL, .PyDLL, etc. look in sys._MEIPASS first.'
#   2: 'Install the hooks.'
#   4: 'the import machinery is not completely set up yet.'
#   5: 'path'
#   6: 'isfile'
#   7: 'join'
#   8: 'sys'
#   9: '_MEIPASS'
#  10: 'basename)'
#  11: 'name'
#  12: 'frozen_name'
#  13: 'oss'
#  14: '%PyInstaller\\loader\\pyimod03_ctypes.py'
#  15: '_frozen_name'
#  16: 'install.<locals>._frozen_name '
#  17: "'install.<locals>.PyInstallerImportError"
#  19: 'msg'
#  20: 'args)'
#  21: 'selfr'
#  22: '  r'
#  23: '__init__'
#  24: '0install.<locals>.PyInstallerImportError.__init__*'
#  25: '__name__'
#  26: '__module__'
#  27: '__qualname__'
#  28: '__firstlineno__r'
#  29: '__static_attributes__'
#  30: 'PyInstallerImportErrorr'
#  31: ' install.<locals>.PyInstallerCDLL'
#  32: 'superr'
#  33: 'Exception)'
#  34: 'kwargs'
#  35: 'base_errorr!'
#  36: '__class__r'
#  37: ')install.<locals>.PyInstallerCDLL.__init__2'
#  38: '__classcell__)'
#  39: 'PyInstallerCDLLr#'
#  40: '!install.<locals>.PyInstallerPyDLL'
#  41: 'Nr&'
#  42: '*install.<locals>.PyInstallerPyDLL.__init__='
#  43: 'PyInstallerPyDLLr4'
#  44: 'winc'
#  45: '"install.<locals>.PyInstallerWinDLL'
#  46: 'Nr&'
#  47: '+install.<locals>.PyInstallerWinDLL.__init__J'
#  48: 'PyInstallerWinDLLr;'
#  49: '"install.<locals>.PyInstallerOleDLL'
#  50: 'Nr&'
#  51: '+install.<locals>.PyInstallerOleDLL.__init__U'
#  52: 'PyInstallerOleDLLrC'
#  53: 'X 5'
#  54: 'PATHz'
#  55: '.dll)'
#  56: 'util'
#  57: 'find_msvcrtr'
#  58: 'environ'
#  59: 'split'
#  60: 'pathsepr'
#  61: 'lower'
#  62: 'endswith)'
#  63: 'search_dirs'
#  64: 'directory'
#  65: 'fname'
#  66: 'ctypesr'
#  67: 'pyinstaller_find_library'
#  68: ')install.<locals>.pyinstaller_find_libraryf'
#  69: 'ImportError'
#  70: 'OSError'
#  71: 'CDLL'
#  72: 'LibraryLoader'
#  73: 'cdll'
#  74: 'PyDLL'
#  75: 'pydllr'
#  76: 'platform'
#  77: 'startswith'
#  78: 'WinDLL'
#  79: 'windll'
#  80: 'OleDLL'
#  81: 'oledll'
#  82: 'ctypes.utilrL'
#  83: 'find_library)'
#  84: '     @@@@r'
#  85: 'installrh'
#  86: 'darwin)'
#  87: 'dyld)'
#  88: '__doc__r'
#  89: 'ctypes.macholibrj'
#  90: 'DEFAULT_LIBRARY_FALLBACK'
#  91: 'insertr'
#  92: '<module>ro'