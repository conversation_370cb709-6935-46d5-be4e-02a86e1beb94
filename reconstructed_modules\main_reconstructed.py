"""
main - 从 .pyc 文件重构
"""

# 可能的导入:
# importlib.resources
# importlib.resourcesr

# 可能的类:
class WORD:
    """重构的类"""
    pass

class S3S49:
    """重构的类"""
    pass

class StringVar:
    """重构的类"""
    pass

class Windowsr:
    """重构的类"""
    pass

class Button:
    """重构的类"""
    pass

class AttributeError:
    """重构的类"""
    pass

class PhotoImage:
    """重构的类"""
    pass

class IsUserAnAdmin:
    """重构的类"""
    pass

class Thread:
    """重构的类"""
    pass

class Style:
    """重构的类"""
    pass

class LabelFrame:
    """重构的类"""
    pass

class VersionCheckerc:
    """重构的类"""
    pass

class ExecutorApp:
    """重构的类"""
    pass

class FramerG:
    """重构的类"""
    pass

class Exception:
    """重构的类"""
    pass

class ScrolledText:
    """重构的类"""
    pass

class NameError:
    """重构的类"""
    pass

class XpRP:
    """重构的类"""
    pass

class S7S8S:
    """重构的类"""
    pass

class Entry:
    """重构的类"""
    pass

class LabelrO:
    """重构的类"""
    pass

class TimeoutExpired:
    """重构的类"""
    pass

# 可能的函数:
def email_va():
    """重构的函数"""
    pass

def clear_btn():
    """重构的函数"""
    pass

def _update_timeout_resultr1():
    """重构的函数"""
    pass

def disabled():
    """重构的函数"""
    pass

def __qualname__():
    """重构的函数"""
    pass

def CREATE_NO_WINDOWrG():
    """重构的函数"""
    pass

def emailrc():
    """重构的函数"""
    pass

def macos():
    """重构的函数"""
    pass

def update_inforc():
    """重构的函数"""
    pass

def sudo():
    """重构的函数"""
    pass

def platform():
    """重构的函数"""
    pass

def geometry():
    """重构的函数"""
    pass

def _update_code_errorr0():
    """重构的函数"""
    pass

def version_labelrJ():
    """重构的函数"""
    pass

def status_label():
    """重构的函数"""
    pass

def deleterE():
    """重构的函数"""
    pass

def darwin():
    """重构的函数"""
    pass

def control_frame():
    """重构的函数"""
    pass

def arm64():
    """重构的函数"""
    pass

def parent():
    """重构的函数"""
    pass

def tkinte():
    """重构的函数"""
    pass

def amd64():
    """重构的函数"""
    pass

def threads():
    """重构的函数"""
    pass

def pathlib():
    """重构的函数"""
    pass

def check_permissions_and_show_info():
    """重构的函数"""
    pass

def subprocess():
    """重构的函数"""
    pass

def showwarning():
    """重构的函数"""
    pass

def gridrE():
    """重构的函数"""
    pass

def status_va():
    """重构的函数"""
    pass

def command():
    """重构的函数"""
    pass

def webbrowse():
    """重构的函数"""
    pass

def set_app_icon():
    """重构的函数"""
    pass

def iconbitmapr0():
    """重构的函数"""
    pass

def message():
    """重构的函数"""
    pass

def __init__():
    """重构的函数"""
    pass

def error_msgs():
    """重构的函数"""
    pass

def __package__():
    """重构的函数"""
    pass

def aarch64():
    """重构的函数"""
    pass

def current_version():
    """重构的函数"""
    pass

def copy_code_btn():
    """重构的函数"""
    pass

def execution_timerc():
    """重构的函数"""
    pass

def icon_path():
    """重构的函数"""
    pass

def title():
    """重构的函数"""
    pass

def windows():
    """重构的函数"""
    pass

def png_pathrE():
    """重构的函数"""
    pass

def rowconfigure():
    """重构的函数"""
    pass

def get_current_version():
    """重构的函数"""
    pass

def threading():
    """重构的函数"""
    pass

def columnconfigure():
    """重构的函数"""
    pass

def generate_email():
    """重构的函数"""
    pass

def insertrE():
    """重构的函数"""
    pass

def codes():
    """重构的函数"""
    pass

def log_message():
    """重构的函数"""
    pass

def base_paths():
    """重构的函数"""
    pass

def info_frame():
    """重构的函数"""
    pass

def timestamp():
    """重构的函数"""
    pass

def textro():
    """重构的函数"""
    pass

def _execute_in_thread():
    """重构的函数"""
    pass

def setup_ui():
    """重构的函数"""
    pass

def getrG():
    """重构的函数"""
    pass

def win32():
    """重构的函数"""
    pass

def package_files():
    """重构的函数"""
    pass

def fileTu():
    """重构的函数"""
    pass

def ctypes():
    """重构的函数"""
    pass

def photo():
    """重构的函数"""
    pass

def _update_execution_result():
    """重构的函数"""
    pass

def returncoderi():
    """重构的函数"""
    pass

def _update_code_resultr1():
    """重构的函数"""
    pass

def email_client():
    """重构的函数"""
    pass

def month():
    """重构的函数"""
    pass

def doc_btn():
    """重构的函数"""
    pass

def __name__():
    """重构的函数"""
    pass

def copy_code():
    """重构的函数"""
    pass

def normalrg():
    """重构的函数"""
    pass

def has_update():
    """重构的函数"""
    pass

def main_frame():
    """重构的函数"""
    pass

def doc_urls():
    """重构的函数"""
    pass

def execute_btn():
    """重构的函数"""
    pass

def seerG():
    """重构的函数"""
    pass

def __file__():
    """重构的函数"""
    pass

def code_entry():
    """重构的函数"""
    pass

def download_urlu():
    """重构的函数"""
    pass

def version_checke():
    """重构的函数"""
    pass

def log_frames():
    """重构的函数"""
    pass

def padxrB():
    """重构的函数"""
    pass

def _check_updates_thread():
    """重构的函数"""
    pass

def runr0():
    """重构的函数"""
    pass

def _MEIPASS():
    """重构的函数"""
    pass

def resource_path():
    """重构的函数"""
    pass

def get_code_btn():
    """重构的函数"""
    pass

def split():
    """重构的函数"""
    pass

def get_verification_code():
    """重构的函数"""
    pass

def windll():
    """重构的函数"""
    pass

def textvariablerh():
    """重构的函数"""
    pass

def _get_code_thread():
    """重构的函数"""
    pass

def result():
    """重构的函数"""
    pass

def is_getting_code():
    """重构的函数"""
    pass

def stateu():
    """重构的函数"""
    pass

def copy_email():
    """重构的函数"""
    pass

def timeout():
    """重构的函数"""
    pass

def current_email():
    """重构的函数"""
    pass

def padding():
    """重构的函数"""
    pass

def style():
    """重构的函数"""
    pass

def max_retries():
    """重构的函数"""
    pass

def padyu():
    """重构的函数"""
    pass

def formatted_messages():
    """重构的函数"""
    pass

def __firstlineno__rV():
    """重构的函数"""
    pass

def fontu():
    """重构的函数"""
    pass

def email_frame():
    """重构的函数"""
    pass

def existsrG():
    """重构的函数"""
    pass

def capture_outputrv():
    """重构的函数"""
    pass

def clipboard_appendri():
    """重构的函数"""
    pass

def root():
    """重构的函数"""
    pass

def stderrrj():
    """重构的函数"""
    pass

def stdout():
    """重构的函数"""
    pass

def wrap():
    """重构的函数"""
    pass

def column():
    """重构的函数"""
    pass

def iconphotor1():
    """重构的函数"""
    pass

def open_documentation():
    """重构的函数"""
    pass

def mail_client():
    """重构的函数"""
    pass

def system_display():
    """重构的函数"""
    pass

def _handle_update_resultr1():
    """重构的函数"""
    pass

def current_date():
    """重构的函数"""
    pass

def sticky():
    """重构的函数"""
    pass

def relative_path():
    """重构的函数"""
    pass

def executable_filenames():
    """重构的函数"""
    pass

def clear_log():
    """重构的函数"""
    pass

def email_entry():
    """重构的函数"""
    pass

def clipboard_clea():
    """重构的函数"""
    pass

def system():
    """重构的函数"""
    pass

def x86_64():
    """重构的函数"""
    pass

def columnspanr():
    """重构的函数"""
    pass

def time():
    """重构的函数"""
    pass

def weight():
    """重构的函数"""
    pass

def afte():
    """重构的函数"""
    pass

def __module__():
    """重构的函数"""
    pass

def name():
    """重构的函数"""
    pass

def shell32():
    """重构的函数"""
    pass

def start_time():
    """重构的函数"""
    pass

def archs():
    """重构的函数"""
    pass

def messagebox():
    """重构的函数"""
    pass

def lowe():
    """重构的函数"""
    pass

def execute_filerj():
    """重构的函数"""
    pass

def _handle_update_errorr0():
    """重构的函数"""
    pass

def executable_path():
    """重构的函数"""
    pass

def is_file():
    """重构的函数"""
    pass

def configure():
    """重构的函数"""
    pass

def code_va():
    """重构的函数"""
    pass

def copy_email_btn():
    """重构的函数"""
    pass

def geteuid():
    """重构的函数"""
    pass

def daemon():
    """重构的函数"""
    pass

def generate_btn():
    """重构的函数"""
    pass

def apps():
    """重构的函数"""
    pass

def reset_frame():
    """重构的函数"""
    pass

def strftime():
    """重构的函数"""
    pass

def askyesno():
    """重构的函数"""
    pass

def latest_version():
    """重构的函数"""
    pass

def get_system_info():
    """重构的函数"""
    pass

def code_frame():
    """重构的函数"""
    pass

def get_executable_filename():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'ttk'
#   2: 'messagebox'
#   3: 'scrolledtext)'
#   4: 'Path)'
#   5: 'datetime)'
#   6: 'files)'
#   7: 'TempMailClient)'
#   8: 'VersionCheckerc'
#   9: 'darwin'
#  10: 'macos'
#  11: 'windows'
#  12: 'linux)'
#  13: 'x86_64'
#  14: 'amd64r'
#  15: 'arm64'
#  16: 'aarch64r'
#  17: 'platform'
#  18: 'system'
#  19: 'lower'
#  20: 'machine)'
#  21: 'archs'
#  22: 'main.py'
#  23: 'get_system_infor'
#  24: 'augment-magic-'
#  25: '.exe)'
#  26: '  r'
#  27: 'get_executable_filenamer'
#  28: 'Windowsr'
#  29: 'ctypes'
#  30: 'windll'
#  31: 'shell32'
#  32: 'IsUserAnAdmin'
#  33: 'geteuid'
#  34: 'is_adminr('
#  35: 'X0-'
#  36: 'N*f'
#  37: 'X0-'
#  38: 'X0-'
#  39: 'importlib.resources'
#  40: 'main)'
#  41: '__package__'
#  42: '__name__'
#  43: 'split'
#  44: 'is_filer'
#  45: 'str'
#  46: 'Exception'
#  47: 'sys'
#  48: '_MEIPASS'
#  49: 'AttributeError'
#  50: '__file__'
#  51: 'parent'
#  52: 'NameError'
#  53: 'cwd)'
#  54: 'relative_path'
#  55: 'package_files'
#  56: 'resource_path'
#  57: 'base_paths'
#  58: '    r'
#  59: 'get_resource_pathr='
#  60: 'A#B'
#  61: 'ExecutorApp'
#  62: 'Augment Magic'
#  63: '900x700'
#  64: 'data/F)'
#  65: 'root'
#  66: 'title'
#  67: 'geometryr'
#  68: 'system_displayr'
#  69: 'executable_pathr'
#  70: 'mail_client'
#  71: 'current_email'
#  72: 'is_getting_coder'
#  73: 'version_checker'
#  74: 'setup_ui'
#  75: 'set_app_icon'
#  76: 'check_permissions_and_show_info'
#  77: 'check_for_updates)'
#  78: 'self'
#  79: 'executable_filenames'
#  80: '  r'
#  81: '__init__'
#  82: 'ExecutorApp.__init__Z'
#  83: 'icons/app_icon.icou'
#  84: 'ICO'
#  85: 'icons/icon_32.pngr'
#  86: 'fileTu'
#  87: 'PNG'
#  88: 'icon_statusr='
#  89: 'existsrG'
#  90: 'iconbitmapr0'
#  91: 'tkinter'
#  92: 'PhotoImage'
#  93: 'iconphotor1'
#  94: 'icon_path'
#  95: 'png_pathrE'
#  96: 'photo'
#  97: '      r'
#  98: 'ExecutorApp.set_app_icon{'
#  99: 'A B>'
# 100: 'O<['
# 101: ': u$'
# 102: 'disabled'
# 103: 'stateu'
# 104: 'log_messager'
# 105: 'execute_btn'
# 106: 'config)'
# 107: '+ExecutorApp.check_permissions_and_show_info'
# 108: 'X R '
# 109: 'S 9'
# 110: 'S!S'
# 111: 'S"S'
# 112: 'X@R.'
# 113: 'S#S$9'
# 114: 'S%U'
# 115: 'S&9'
# 116: "S'9"
# 117: 'S(U'
# 118: 'S)S*9'
# 119: 'S+9'
# 120: 'S,S'
# 121: 'S-S'
# 122: 'XPR<'
# 123: 'S#S$9'
# 124: 'S.U'
# 125: 'S)S*9'
# 126: "S'9"
# 127: 'S(U'
# 128: 'S)S*9'
# 129: 'S+9'
# 130: 'S/S'
# 131: 'S0U'
# 132: 'S&9'
# 133: 'S+9'
# 134: 'S1S'
# 135: 'S2U'
# 136: 'S&9'
# 137: "S'9"
# 138: 'S3S49'
# 139: 'XpRP'
# 140: 'S59'
# 141: 'S6S+9'
# 142: 'S7S8S'
# 143: 'S99'
# 144: 'S:S;9'
# 145: 'Ns='
# 146: ')<N'
# 147: 'paddingr'
# 148: 'row'
# 149: 'column'
# 150: 'sticky'
# 151: 'weight'
# 152: 'textro'
# 153: 'columnspanrr'
# 154: 'padyu'
# 155: 'padxrB'
# 156: 'bold)'
# 157: 'fontu'
# 158: 'Accent.TButton)'
# 159: 'command'
# 160: 'style'
# 161: 'blue)'
# 162: 'foregroundr~'
# 163: 'readonly)'
# 164: 'textvariablerh'
# 165: 'value)'
# 166: 'wrap'
# 167: 'height).r'
# 168: 'FramerG'
# 169: 'gridrE'
# 170: 'columnconfigure'
# 171: 'rowconfigure'
# 172: 'LabelFrame'
# 173: 'LabelrO'
# 174: 'get_current_version'
# 175: 'version_labelrJ'
# 176: 'Button'
# 177: 'open_documentation'
# 178: 'doc_btn'
# 179: 'Style'
# 180: 'configure'
# 181: 'StringVar'
# 182: 'email_var'
# 183: 'Entry'
# 184: 'email_entry'
# 185: 'generate_email'
# 186: 'generate_btn'
# 187: 'copy_email'
# 188: 'copy_email_btn'
# 189: 'code_var'
# 190: 'code_entry'
# 191: 'get_verification_code'
# 192: 'get_code_btn'
# 193: 'copy_code'
# 194: 'copy_code_btn'
# 195: 'execute_filerj'
# 196: 'clear_log'
# 197: 'clear_btn'
# 198: 'status_var'
# 199: 'status_labelr'
# 200: 'ScrolledText'
# 201: 'WORD'
# 202: 'log_text)'
# 203: 'main_frame'
# 204: 'info_framer'
# 205: 'email_frame'
# 206: 'code_frame'
# 207: 'reset_frame'
# 208: 'control_frame'
# 209: 'log_frames'
# 210: '         r'
# 211: 'ExecutorApp.setup_ui'
# 212: '%H:%M:%S'
# 213: 'now'
# 214: 'strftimer'
# 215: 'insertrE'
# 216: 'END'
# 217: 'seerG'
# 218: 'update_idletasks)'
# 219: 'message'
# 220: 'timestamp'
# 221: 'formatted_messages'
# 222: '    r'
# 223: 'ExecutorApp.log_messageD'
# 224: '?N)'
# 225: 'deleterE'
# 226: 'ExecutorApp.clear_logM'
# 227: 'Rhttps://frequent-city-667.notion.site/augmentcode-21e22957056580659e0bcdf2ae6f57cdN)'
# 228: 'webbrowser'
# 229: 'open)'
# 230: 'doc_urls'
# 231: '  r'
# 232: 'ExecutorApp.open_documentationQ'
# 233: ': N)'
# 234: 'showerror)'
# 235: '   r'
# 236: '.A)'
# 237: 'sudo'
# 238: ': rf'
# 239: 'targetT)'
# 240: 'month'
# 241: 'yearr('
# 242: 'set'
# 243: 'threading'
# 244: 'Thread'
# 245: '_execute_in_thread'
# 246: 'daemon'
# 247: 'start)'
# 248: 'current_date'
# 249: 'threads'
# 250: '   r'
# 251: 'ExecutorApp.execute_file`'
# 252: 'X#5'
# 253: ': Ti,'
# 254: 'win32r'
# 255: 'capture_outputrv'
# 256: 'timeout'
# 257: 'creationflagsN)'
# 258: 'name'
# 259: 'time'
# 260: 'subprocess'
# 261: 'runr0'
# 262: 'CREATE_NO_WINDOWrG'
# 263: 'after'
# 264: '_update_execution_result'
# 265: 'TimeoutExpired'
# 266: '_update_timeout_resultr1'
# 267: '_update_error_result)'
# 268: 'start_time'
# 269: 'result'
# 270: 'execution_timerc'
# 271: '     r'
# 272: 'ExecutorApp._execute_in_thread~'
# 273: 'O:U'
# 274: ': u'
# 275: ': z'
# 276: '.2fu'
# 277: 'normalrg'
# 278: 'returncoderi'
# 279: 'stdout'
# 280: 'stderrrj'
# 281: '   r'
# 282: '$ExecutorApp._update_execution_result'
# 283: '"ExecutorApp._update_timeout_result'
# 284: ': u'
# 285: 'error_msgs'
# 286: '  r'
# 287: ' ExecutorApp._update_error_result'
# 288: '...r'
# 289: ': u'
# 290: ': r'
# 291: ': N)'
# 292: 'emailrc'
# 293: '   r'
# 294: 'ExecutorApp.generate_email'
# 295: 'B-B0'
# 296: ':.C-'
# 297: 'clipboard_clear'
# 298: 'clipboard_appendri'
# 299: 'showinfo)'
# 300: 'ExecutorApp.copy_email'
# 301: 'getrG'
# 302: 'codes'
# 303: '  r'
# 304: 'ExecutorApp.copy_code'
# 305: '...Nu'
# 306: 'Trf'
# 307: '...r'
# 308: 'showwarningr'
# 309: '_get_code_threadr'
# 310: '  r'
# 311: '!ExecutorApp.get_verification_code'
# 312: '...ru'
# 313: 'max_retries'
# 314: 'retry_intervalN)'
# 315: '_update_code_resultr1'
# 316: '_update_code_errorr0'
# 317: '   r'
# 318: 'ExecutorApp._get_code_thread'
# 319: 'A(A+'
# 320: '50B*'
# 321: ': u'
# 322: ': u'
# 323: '  r'
# 324: 'ExecutorApp._update_code_result'
# 325: ': u'
# 326: ': N)'
# 327: '  r'
# 328: 'ExecutorApp._update_code_error'
# 329: 'Nu '
# 330: '...ru'
# 331: '_handle_update_resultr1'
# 332: '_handle_update_errorr0'
# 333: 'update_inforc'
# 334: '_check_updates_thread'
# 335: '<ExecutorApp.check_for_updates.<locals>._check_updates_thread&'
# 336: 'TN)'
# 337: '`  r'
# 338: 'ExecutorApp.check_for_updates#'
# 339: 'has_update'
# 340: 'latest_version'
# 341: 'current_versionr'
# 342: ': vr'
# 343: 'orange)'
# 344: ': vu'
# 345: ': vu'
# 346: ' vu'
# 347: ': vu '
# 348: 'download_urlu'
# 349: ': u'
# 350: 'green)'
# 351: 'askyesnor'
# 352: '     r'
# 353: '!ExecutorApp._handle_update_result6'
# 354: ': N)'
# 355: '  r'
# 356: ' ExecutorApp._handle_update_error['
# 357: 'mainloop)'
# 358: 'ExecutorApp.run_'
# 359: '__module__'
# 360: '__qualname__'
# 361: '__firstlineno__rV'
# 362: "__static_attributes__r'"
# 363: 'apps'
# 364: '__main__)'
# 365: 'pathlibr'
# 366: 'importlib.resourcesr'
# 367: 'email_clientr'
# 368: '<module>rC'