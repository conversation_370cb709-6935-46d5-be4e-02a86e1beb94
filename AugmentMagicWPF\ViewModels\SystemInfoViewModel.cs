using System.Windows.Media;
using AugmentMagicWPF.Services;

namespace AugmentMagicWPF.ViewModels
{
    public class SystemInfoViewModel : BaseViewModel
    {
        private readonly ISystemInfoService _systemInfoService;

        public SystemInfoViewModel(ISystemInfoService systemInfoService)
        {
            _systemInfoService = systemInfoService;
            LoadSystemInfo();
        }

        public string SystemName { get; private set; } = string.Empty;
        public string Architecture { get; private set; } = string.Empty;
        public string Version { get; private set; } = string.Empty;
        public bool IsAdmin { get; private set; }
        public string IsAdminText => IsAdmin ? "是" : "否";
        public Brush IsAdminBrush => IsAdmin ? 
            new SolidColorBrush(Color.FromRgb(76, 175, 80)) : // Green
            new SolidColorBrush(Color.FromRgb(244, 67, 54));   // Red

        private void LoadSystemInfo()
        {
            var systemInfo = _systemInfoService.GetSystemInfo();
            SystemName = systemInfo.SystemName;
            Architecture = systemInfo.Architecture;
            Version = systemInfo.Version;
            IsAdmin = systemInfo.IsAdmin;

            OnPropertyChanged(nameof(SystemName));
            OnPropertyChanged(nameof(Architecture));
            OnPropertyChanged(nameof(Version));
            OnPropertyChanged(nameof(IsAdmin));
            OnPropertyChanged(nameof(IsAdminText));
            OnPropertyChanged(nameof(IsAdminBrush));
        }
    }
}
