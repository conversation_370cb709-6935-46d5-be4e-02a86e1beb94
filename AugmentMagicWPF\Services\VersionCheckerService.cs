using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public class VersionCheckerService : IVersionCheckerService
    {
        private readonly HttpClient _httpClient;
        private readonly string _updateUrl = "https://api.github.com/repos/augment-code/augment-magic/releases/latest";

        public VersionCheckerService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "AugmentMagic/1.0.0");
        }

        public async Task<bool> CheckForUpdatesAsync()
        {
            try
            {
                var latestVersion = await GetLatestVersionInfoAsync();
                if (latestVersion == null)
                    return false;

                var currentVersion = GetCurrentVersion();
                return CompareVersions(currentVersion, latestVersion.Version);
            }
            catch
            {
                return false;
            }
        }

        public async Task<VersionInfo?> GetLatestVersionInfoAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync(_updateUrl);
                if (!response.IsSuccessStatusCode)
                    return null;

                var json = await response.Content.ReadAsStringAsync();
                var githubRelease = JsonSerializer.Deserialize<GitHubRelease>(json);

                if (githubRelease == null)
                    return null;

                return new VersionInfo
                {
                    Version = githubRelease.tag_name?.TrimStart('v') ?? "1.0.0",
                    Name = githubRelease.name ?? "Unknown",
                    Description = githubRelease.body ?? "",
                    PublishedAt = githubRelease.published_at,
                    DownloadUrl = githubRelease.html_url ?? "",
                    IsPrerelease = githubRelease.prerelease,
                    ReleaseNotes = githubRelease.body ?? "",
                    Assets = githubRelease.assets?.Select(a => new VersionAsset
                    {
                        Name = a.name ?? "",
                        DownloadUrl = a.browser_download_url ?? "",
                        Size = a.size,
                        ContentType = a.content_type ?? ""
                    }).ToList() ?? new List<VersionAsset>()
                };
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> DownloadUpdateAsync(string downloadUrl, string savePath, IProgress<double>? progress = null)
        {
            try
            {
                using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
                if (!response.IsSuccessStatusCode)
                    return false;

                var totalBytes = response.Content.Headers.ContentLength ?? 0;
                var downloadedBytes = 0L;

                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(savePath, FileMode.Create, FileAccess.Write, FileShare.None);

                var buffer = new byte[8192];
                int bytesRead;

                while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;

                    if (totalBytes > 0)
                    {
                        var progressPercentage = (double)downloadedBytes / totalBytes * 100;
                        progress?.Report(progressPercentage);
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public string GetCurrentVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.0.0.0";
            }
            catch
            {
                return "1.0.0.0";
            }
        }

        public bool CompareVersions(string currentVersion, string latestVersion)
        {
            try
            {
                var current = new Version(currentVersion);
                var latest = new Version(latestVersion);
                return latest > current;
            }
            catch
            {
                // If version parsing fails, use string comparison
                return string.Compare(latestVersion, currentVersion, StringComparison.OrdinalIgnoreCase) > 0;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        // GitHub API response models
        private class GitHubRelease
        {
            public string? tag_name { get; set; }
            public string? name { get; set; }
            public string? body { get; set; }
            public DateTime published_at { get; set; }
            public string? html_url { get; set; }
            public bool prerelease { get; set; }
            public List<GitHubAsset>? assets { get; set; }
        }

        private class GitHubAsset
        {
            public string? name { get; set; }
            public string? browser_download_url { get; set; }
            public long size { get; set; }
            public string? content_type { get; set; }
        }
    }
}
