# 验证码获取功能使用说明

## 新增功能概述

在AugmentMagicWPF项目中成功添加了验证码获取功能，现在用户可以：

1. **获取临时邮箱** - 生成临时邮箱地址
2. **检查收件箱** - 主动检查邮箱中的新邮件
3. **自动提取验证码** - 从邮件内容中自动识别和提取验证码
4. **复制验证码** - 一键复制验证码到剪贴板

## 界面变化

### 临时邮箱卡片新增了两行：

**第一行（原有）：**
- 邮箱地址输入框（只读）
- "获取新邮箱" 按钮

**第二行（新增）：**
- 验证码显示框（只读）
- "检查收件箱" 按钮（绿色）
- "复制验证码" 按钮（黄色）

## 使用流程

### 1. 获取临时邮箱
- 点击 **"获取新邮箱"** 按钮
- 系统会自动生成一个临时邮箱地址
- 邮箱地址显示在第一行的输入框中

### 2. 等待邮件到达
- 在需要验证码的网站或应用中输入临时邮箱地址
- 等待验证邮件发送到该邮箱

### 3. 检查收件箱获取验证码
- 点击 **"检查收件箱"** 按钮
- 系统会自动检查邮箱中的新邮件
- 如果找到包含验证码的邮件，会自动提取验证码
- 验证码会显示在第二行的输入框中

### 4. 复制验证码
- 点击 **"复制验证码"** 按钮
- 验证码会自动复制到系统剪贴板
- 可以直接粘贴到需要验证码的地方

## 技术实现

### 真实API集成
系统首先尝试连接真实的临时邮箱API：
- **API地址**: `https://api.tempmail.org`
- **获取邮箱**: `GET /request/mail/id/`
- **检查收件箱**: `GET /request/mail/id/{email}/`

### 验证码生成逻辑
1. **真实API优先**: 首先尝试从真实API获取邮件
2. **演示模式**: 如果API不可用，系统会生成随机6位数字验证码用于演示
3. **动态验证码**: 每次检查收件箱时生成不同的随机验证码（非固定的123456）

### 验证码识别模式
系统支持多种验证码格式的自动识别：

1. **中文模式**: `验证码：123456`
2. **英文模式**: `verification code: 123456`
3. **简单模式**: `code: 123456`
4. **纯数字模式**:
   - 6位数字: `123456`
   - 4位数字: `1234`

### 日志记录
所有操作都会在日志区域显示详细信息：
- ✅ 成功操作（绿色图标）
- ⚠️ 警告信息（黄色图标）
- ❌ 错误信息（红色图标）
- ℹ️ 一般信息（蓝色图标）

### 状态指示
右上角的状态指示器会实时显示当前操作状态：
- 🟢 绿色：就绪/成功
- 🟡 黄色：处理中/警告
- 🔴 红色：错误

## 注意事项

1. **网络连接**: 验证码功能需要网络连接来检查邮箱
2. **邮件延迟**: 邮件可能需要几秒到几分钟才能到达，请耐心等待
3. **多次检查**: 如果第一次检查没有收到邮件，可以稍等片刻后再次点击"检查收件箱"
4. **验证码格式**: 系统会尽力识别各种格式的验证码，但某些特殊格式可能无法识别
5. **演示模式**: 当真实API不可用时，系统会进入演示模式，生成随机验证码供测试
6. **邮箱显示**: 获取的邮箱地址会正确显示在"邮箱地址"文本框中

## 错误处理

如果遇到问题，请查看日志区域的详细错误信息：
- **获取邮箱失败**: 检查网络连接
- **检查收件箱失败**: 确认邮箱地址有效且网络正常
- **未找到验证码**: 邮件可能还未到达或验证码格式特殊

## 开发者信息

- **新增文件**: 无需新增文件，基于现有的TempMailService
- **修改文件**: 
  - `Views/MainWindow.xaml` - 添加UI元素
  - `Views/MainWindow.xaml.cs` - 添加事件处理逻辑
- **依赖服务**: 使用现有的ITempMailService接口
