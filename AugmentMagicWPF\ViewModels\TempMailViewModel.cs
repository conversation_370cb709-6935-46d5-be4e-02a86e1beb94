using System;
using System.Linq;
using System.Windows.Input;
using AugmentMagicWPF.Services;

namespace AugmentMagicWPF.ViewModels
{
    public class TempMailViewModel : BaseViewModel
    {
        private readonly ITempMailService _tempMailService;
        private readonly ILoggerService _loggerService;
        private string _currentEmail = string.Empty;
        private bool _isLoading = false;

        public TempMailViewModel(ITempMailService tempMailService, ILoggerService loggerService)
        {
            _tempMailService = tempMailService;
            _loggerService = loggerService;

            GetNewEmailCommand = new RelayCommand(GetNewEmail, CanGetNewEmail);
            CheckInboxCommand = new RelayCommand(CheckInbox, CanCheckInbox);
        }

        public string CurrentEmail
        {
            get => _currentEmail;
            set => SetProperty(ref _currentEmail, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public ICommand GetNewEmailCommand { get; }
        public ICommand CheckInboxCommand { get; }

        private async void GetNewEmail()
        {
            try
            {
                IsLoading = true;
                _loggerService.LogInfo("正在获取新的临时邮箱...");

                var email = await _tempMailService.GetNewEmailAsync();
                CurrentEmail = email;

                _loggerService.LogSuccess($"获取到新邮箱: {email}");
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"获取邮箱失败: {ex.Message}");
                CurrentEmail = string.Empty;
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void CheckInbox()
        {
            if (string.IsNullOrEmpty(CurrentEmail))
            {
                _loggerService.LogWarning("请先获取邮箱地址");
                return;
            }

            try
            {
                IsLoading = true;
                _loggerService.LogInfo("正在检查收件箱...");

                var emails = await _tempMailService.CheckInboxAsync(CurrentEmail);
                
                if (emails.Any())
                {
                    _loggerService.LogSuccess($"收到 {emails.Count()} 封邮件");
                    
                    foreach (var email in emails)
                    {
                        _loggerService.LogInfo($"邮件: {email.Subject} - {email.From}");
                        
                        // Try to extract verification code
                        var code = _tempMailService.ExtractVerificationCode(email.Body);
                        if (!string.IsNullOrEmpty(code))
                        {
                            _loggerService.LogSuccess($"找到验证码: {code}");
                        }
                    }
                }
                else
                {
                    _loggerService.LogInfo("收件箱为空");
                }
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"检查收件箱失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanGetNewEmail()
        {
            return !IsLoading;
        }

        private bool CanCheckInbox()
        {
            return !IsLoading && !string.IsNullOrEmpty(CurrentEmail);
        }
    }
}
