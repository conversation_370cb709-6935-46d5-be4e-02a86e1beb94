"""
pyimod04_pywin32 - 从 .pyc 文件重构
"""

# 可能的函数:
def path():
    """重构的函数"""
    pass

def install():
    """重构的函数"""
    pass

def environ():
    """重构的函数"""
    pass

def pywin32_system32():
    """重构的函数"""
    pass

def __doc__():
    """重构的函数"""
    pass

def _MEIPASS():
    """重构的函数"""
    pass

def pythonwin():
    """重构的函数"""
    pass

def append():
    """重构的函数"""
    pass

def pywin32_system32_paths():
    """重构的函数"""
    pass

def pywin32_ext_paths():
    """重构的函数"""
    pass

def extend():
    """重构的函数"""
    pass

def pywin32_ext_path():
    """重构的函数"""
    pass

def add_dll_directory():
    """重构的函数"""
    pass

def join():
    """重构的函数"""
    pass

def win32():
    """重构的函数"""
    pass

def isdi():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   2: 'instead of per-module runtime hook scripts.'
#   3: 'win32'
#   4: 'pythonwin'
#   5: 'pywin32_system32'
#   6: 'PATH)'
#   7: 'path'
#   8: 'join'
#   9: 'sys'
#  10: '_MEIPASS'
#  11: 'isdir'
#  12: 'extend'
#  13: 'append'
#  14: 'add_dll_directory'
#  15: 'environ'
#  16: 'get'
#  17: 'pathsep)'
#  18: 'pywin32_ext_paths'
#  19: 'pywin32_ext_pathr'
#  20: 'pywin32_system32_paths'
#  21: '&PyInstaller\\loader\\pyimod04_pywin32.py'
#  22: 'installr'
#  23: '8E4'
#  24: '(E9'
#  25: '__doc__r'
#  26: '<module>r'