using System.Collections.Generic;

namespace AugmentMagicWPF.ViewModels
{
    public class SettingsViewModel : BaseViewModel
    {
        private bool _autoCheckUpdates = true;
        private bool _enableLogging = true;
        private string _logLevel = "Info";
        private string _tempMailProvider = "TempMail.org";

        public bool AutoCheckUpdates
        {
            get => _autoCheckUpdates;
            set => SetProperty(ref _autoCheckUpdates, value);
        }

        public bool EnableLogging
        {
            get => _enableLogging;
            set => SetProperty(ref _enableLogging, value);
        }

        public string LogLevel
        {
            get => _logLevel;
            set => SetProperty(ref _logLevel, value);
        }

        public string TempMailProvider
        {
            get => _tempMailProvider;
            set => SetProperty(ref _tempMailProvider, value);
        }

        public List<string> LogLevels { get; } = new List<string>
        {
            "Debug",
            "Info", 
            "Warning",
            "Error"
        };

        public List<string> TempMailProviders { get; } = new List<string>
        {
            "TempMail.org",
            "10MinuteMail.com",
            "GuerillaMail.com",
            "Mailinator.com"
        };
    }
}
