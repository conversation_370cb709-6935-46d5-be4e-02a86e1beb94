'\naugmentedAssign.py -- source test pattern for augmented assigns\n\nThis source is part of the decompyle test suite.\n\ndecompyle is a Python byte-code decompiler\nSee http://www.goebel-consult.de/decompyle/ for download and\nfor further information\n' <EOL>
raise 'This program can\'t be run' <EOL>
a = 1 <EOL>
b = 2 <EOL>
a += b <EOL>
print a <EOL>
a -= b <EOL>
print a <EOL>
a *= b <EOL>
print a <EOL>
a -= a <EOL>
print a <EOL>
a += 7 * 3 <EOL>
print a <EOL>
l = [ 1 , 2 , 3 ] <EOL>
l [ 1 ] *= 3 <EOL>
print l [ 1 ] <EOL>
l [ 1 ] [ 2 ] [ 3 ] = 7 <EOL>
l [ 1 ] [ 2 ] [ 3 ] *= 3 <EOL>
l [ : ] += [ 9 ] <EOL>
print l <EOL>
l [ : 2 ] += [ 9 ] <EOL>
print l <EOL>
l [ 1 : ] += [ 9 ] <EOL>
print l <EOL>
l [ 1 : 4 ] += [ 9 ] <EOL>
print l <EOL>
l += [ 42 , 43 ] <EOL>
print l <EOL>
a . value = 1 <EOL>
a . value += 1 <EOL>
a . b . val = 1 <EOL>
a . b . val += 1 <EOL>
l = [ ] <EOL>
for i in range ( 3 ) : <EOL>
<INDENT>
lj = [ ] <EOL>
for j in range ( 3 ) : <EOL>
<INDENT>
lk = [ ] <EOL>
for k in range ( 3 ) : <EOL>
<INDENT>
lk . append ( 0 ) <EOL>
<OUTDENT>
lj . append ( lk ) <EOL>
<OUTDENT>
l . append ( lj ) <EOL>
<OUTDENT>
i = 1 <EOL>
j = 1 <EOL>
k = 1 <EOL>
def f ( ) : <EOL>
<INDENT>
global i <EOL>
i += 1 <EOL>
return i <EOL>
<OUTDENT>
l [ i ] [ j ] [ k ] = 1 <EOL>
i = 1 <EOL>
l [ f ( ) ] [ j ] [ k ] += 1 <EOL>
print i , l <EOL>
