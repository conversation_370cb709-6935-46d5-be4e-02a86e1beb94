using System.Collections.ObjectModel;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.ViewModels
{
    public class LogViewModel : BaseViewModel
    {
        public LogViewModel()
        {
            LogEntries = new ObservableCollection<LogEntry>();
        }

        public ObservableCollection<LogEntry> LogEntries { get; }

        public void AddLogEntry(LogEntry entry)
        {
            LogEntries.Add(entry);
            
            // Keep only last 1000 entries
            while (LogEntries.Count > 1000)
            {
                LogEntries.RemoveAt(0);
            }
        }

        public void ClearLog()
        {
            LogEntries.Clear();
        }
    }
}
