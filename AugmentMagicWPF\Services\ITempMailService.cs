using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public interface ITempMailService
    {
        Task<string> GetNewEmailAsync();
        Task<IEnumerable<EmailMessage>> CheckInboxAsync(string email);
        Task<EmailMessage?> GetEmailContentAsync(string emailId);
        string ExtractVerificationCode(string content);
        Task<bool> WaitForEmailAsync(string email, TimeSpan timeout);
    }
}
