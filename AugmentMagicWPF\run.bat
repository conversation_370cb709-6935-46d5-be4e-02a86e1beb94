@echo off
echo ========================================
echo      Running Augment Magic WPF
echo ========================================
echo.

REM Check if .NET 8.0 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed or not in PATH
    echo Please install .NET 8.0 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo Starting application...
echo.
dotnet run

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Application failed to start
    echo Check the error messages above for details
    pause
    exit /b 1
)

echo.
echo Application closed.
pause
