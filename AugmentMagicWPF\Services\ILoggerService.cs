using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public interface ILoggerService
    {
        event EventHandler<LogEntry>? LogEntryAdded;
        
        void LogDebug(string message);
        void LogInfo(string message);
        void LogWarning(string message);
        void LogError(string message);
        void LogSuccess(string message);
        
        IEnumerable<LogEntry> GetLogEntries();
        void ClearLog();
        Task SaveLogToFileAsync(string filePath);
    }
}
