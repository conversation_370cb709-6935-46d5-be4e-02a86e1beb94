using System;
using System.Windows.Media;

namespace AugmentMagicWPF.Models
{
    public class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;

        public Brush LevelBrush => Level switch
        {
            LogLevel.Debug => new SolidColorBrush(Color.FromRgb(158, 158, 158)),    // Gray
            LogLevel.Info => new SolidColorBrush(Color.FromRgb(33, 150, 243)),      // Blue
            LogLevel.Warning => new SolidColorBrush(Color.FromRgb(255, 152, 0)),    // Orange
            LogLevel.Error => new SolidColorBrush(Color.FromRgb(244, 67, 54)),      // Red
            LogLevel.Success => new SolidColorBrush(Color.FromRgb(76, 175, 80)),    // Green
            _ => new SolidColorBrush(Color.FromRgb(255, 255, 255))                  // White
        };

        public Brush MessageBrush => Level switch
        {
            LogLevel.Error => new SolidColorBrush(Color.FromRgb(255, 205, 210)),    // Light Red
            LogLevel.Warning => new SolidColorBrush(Color.FromRgb(255, 224, 178)),  // Light Orange
            LogLevel.Success => new SolidColorBrush(Color.FromRgb(200, 230, 201)),  // Light Green
            _ => new SolidColorBrush(Color.FromRgb(255, 255, 255))                  // White
        };
    }

    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Success
    }
}
