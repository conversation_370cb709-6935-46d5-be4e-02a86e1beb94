#!/usr/bin/env python3
"""
AugmentMagic - 重构的主程序文件
基于从 .pyc 文件中提取的字符串和模式重构
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from pathlib import Path
from datetime import datetime
import platform
import ctypes
import os
import sys

# 导入自定义模块（基于字符串分析）
try:
    from files import *
except ImportError:
    pass

try:
    from TempMailClient import TempMailClient
except ImportError:
    class TempMailClient:
        def __init__(self):
            pass

try:
    from VersionChecker import VersionChecker
except ImportError:
    class VersionChecker:
        def __init__(self):
            pass


def get_system_info():
    """获取系统信息，返回操作系统和架构"""
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    # 系统名称映射
    if system == 'darwin':
        system = 'macos'
    elif system == 'windows':
        system = 'windows'
    else:
        system = 'linux'
    
    # 架构映射
    if arch in ['x86_64', 'amd64']:
        arch = 'x86_64'
    elif arch in ['arm64', 'aarch64']:
        arch = 'aarch64'
    
    return system, arch


def get_executable_filename():
    """根据当前系统获取对应的可执行文件名"""
    system, arch = get_system_info()
    filename = f"augment-magic-{system}-{arch}"
    if system == 'windows':
        filename += '.exe'
    return filename


def is_admin():
    """检查当前程序是否以管理员权限运行"""
    try:
        if platform.system() == 'Windows':
            return ctypes.windll.shell32.IsUserAnAdmin()
        else:
            return os.geteuid() == 0
    except:
        return False


def get_resource_path(relative_path):
    """获取资源文件的绝对路径，使用importlib.resources标准库"""
    try:
        # 尝试使用 importlib.resources
        import importlib.resources
        if '.' in __package__ or __name__.split('.'):
            package_files = importlib.resources.files(__package__ or 'main')
            resource_path = package_files / relative_path
            if resource_path.is_file():
                return str(resource_path)
    except Exception:
        pass
    
    try:
        # 尝试使用 PyInstaller 的 _MEIPASS
        base_path = sys._MEIPASS
        return os.path.join(base_path, relative_path)
    except AttributeError:
        pass
    
    try:
        # 使用 __file__ 的父目录
        base_path = Path(__file__).parent
        return str(base_path / relative_path)
    except NameError:
        # 使用当前工作目录
        base_path = Path.cwd()
        return str(base_path / relative_path)


class ExecutorApp:
    """主应用程序类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment Magic")
        self.root.geometry("900x700")
        
        # 获取系统信息
        system, arch = get_system_info()
        self.system_display = f"{system}-{arch}"
        
        # 获取可执行文件路径
        executable_filename = get_executable_filename()
        self.executable_path = get_resource_path(f"data/{executable_filename}")
        
        # 初始化邮件客户端
        self.mail_client = TempMailClient()
        self.current_email = ""
        self.is_getting_code = False
        
        # 初始化版本检查器
        self.version_checker = VersionChecker()
        
        # 设置UI
        self.setup_ui()
        
        # 设置应用图标
        self.set_app_icon()
        
        # 检查权限并显示信息
        self.check_permissions_and_show_info()
        
        # 检查更新
        self.check_for_updates()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Augment Magic", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 创建文本区域
        self.text_area = scrolledtext.ScrolledText(main_frame, 
                                                  wrap=tk.WORD, 
                                                  width=80, 
                                                  height=30)
        self.text_area.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 添加按钮
        ttk.Button(button_frame, text="开始", command=self.start_process).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="停止", command=self.stop_process).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清除", command=self.clear_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT)
    
    def set_app_icon(self):
        """设置应用图标"""
        try:
            # 尝试加载 ICO 图标
            icon_path = get_resource_path("icons/app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
                self.log_message("ICO图标加载成功")
                return
        except Exception:
            pass
        
        try:
            # 尝试加载 PNG 图标
            icon_path = get_resource_path("icons/icon_32.png")
            if os.path.exists(icon_path):
                import tkinter as tk
                photo = tk.PhotoImage(file=icon_path)
                self.root.iconphoto(True, photo)
                self.log_message("PNG图标加载成功")
                return
        except Exception as e:
            self.log_message(f"图标加载失败: {e}")
        
        self.log_message("未找到图标文件")
    
    def log_message(self, message):
        """在文本区域记录消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.text_area.insert(tk.END, log_entry)
        self.text_area.see(tk.END)
        self.root.update_idletasks()
    
    def start_process(self):
        """开始处理"""
        self.log_message("开始处理...")
        # 这里添加具体的处理逻辑
    
    def stop_process(self):
        """停止处理"""
        self.log_message("停止处理...")
    
    def clear_text(self):
        """清除文本区域"""
        self.text_area.delete(1.0, tk.END)
    
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""Augment Magic
        
系统信息: {self.system_display}
可执行文件: {os.path.basename(self.executable_path) if self.executable_path else '未找到'}
管理员权限: {'是' if is_admin() else '否'}

这是一个从 PyInstaller 打包文件重构的应用程序。
"""
        messagebox.showinfo("关于", about_text)
    
    def check_permissions_and_show_info(self):
        """检查权限并显示信息"""
        self.log_message(f"系统信息: {self.system_display}")
        self.log_message(f"管理员权限: {'是' if is_admin() else '否'}")
        if self.executable_path and os.path.exists(self.executable_path):
            self.log_message(f"可执行文件: {self.executable_path}")
        else:
            self.log_message("警告: 未找到可执行文件")
    
    def check_for_updates(self):
        """检查更新"""
        self.log_message("检查更新...")
        # 这里添加版本检查逻辑
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    app = ExecutorApp()
    app.run()


if __name__ == "__main__":
    main()
