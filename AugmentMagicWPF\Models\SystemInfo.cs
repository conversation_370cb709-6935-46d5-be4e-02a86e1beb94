namespace AugmentMagicWPF.Models
{
    public class SystemInfo
    {
        public string SystemName { get; set; } = string.Empty;
        public string Architecture { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public bool IsAdmin { get; set; }
        public string MachineName { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public long TotalMemory { get; set; }
        public long AvailableMemory { get; set; }
        public string ProcessorName { get; set; } = string.Empty;
        public int ProcessorCores { get; set; }
        public string DotNetVersion { get; set; } = string.Empty;
    }
}
