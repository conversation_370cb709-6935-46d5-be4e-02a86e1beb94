using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using AugmentMagicWPF.Services;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.ViewModels
{
    public class MainWindowViewModel : BaseViewModel
    {
        private readonly ISystemInfoService _systemInfoService;
        private readonly ITempMailService _tempMailService;
        private readonly IVersionCheckerService _versionCheckerService;
        private readonly IExecutorService _executorService;
        private readonly ILoggerService _loggerService;

        private string _status = "就绪";
        private double _progress = 0;
        private bool _isProgressVisible = false;
        private string _currentTime = DateTime.Now.ToString("HH:mm:ss");

        public MainWindowViewModel(
            ISystemInfoService systemInfoService,
            ITempMailService tempMailService,
            IVersionCheckerService versionCheckerService,
            IExecutorService executorService,
            ILoggerService loggerService)
        {
            _systemInfoService = systemInfoService;
            _tempMailService = tempMailService;
            _versionCheckerService = versionCheckerService;
            _executorService = executorService;
            _loggerService = loggerService;

            // Initialize ViewModels
            SystemInfo = new SystemInfoViewModel(_systemInfoService);
            TempMail = new TempMailViewModel(_tempMailService, _loggerService);
            LogEntries = new ObservableCollection<LogEntry>();

            // Initialize Commands
            StartExecutionCommand = new RelayCommand(StartExecution, CanStartExecution);
            StopExecutionCommand = new RelayCommand(StopExecution, CanStopExecution);
            ClearLogCommand = new RelayCommand(ClearLog);
            SaveLogCommand = new RelayCommand(SaveLog);
            OpenSettingsCommand = new RelayCommand(OpenSettings);
            ShowAboutCommand = new RelayCommand(ShowAbout);

            // Subscribe to logger events
            _loggerService.LogEntryAdded += OnLogEntryAdded;

            // Start time update timer
            StartTimeUpdateTimer();

            // Initialize
            Initialize();
        }

        public SystemInfoViewModel SystemInfo { get; }
        public TempMailViewModel TempMail { get; }
        public ObservableCollection<LogEntry> LogEntries { get; }

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public double Progress
        {
            get => _progress;
            set => SetProperty(ref _progress, value);
        }

        public bool IsProgressVisible
        {
            get => _isProgressVisible;
            set => SetProperty(ref _isProgressVisible, value);
        }

        public string CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        public ICommand StartExecutionCommand { get; }
        public ICommand StopExecutionCommand { get; }
        public ICommand ClearLogCommand { get; }
        public ICommand SaveLogCommand { get; }
        public ICommand OpenSettingsCommand { get; }
        public ICommand ShowAboutCommand { get; }

        private async void Initialize()
        {
            _loggerService.LogInfo("应用程序启动");
            _loggerService.LogInfo($"系统信息: {SystemInfo.SystemName} {SystemInfo.Architecture}");
            _loggerService.LogInfo($"管理员权限: {SystemInfo.IsAdminText}");

            // Check for updates
            try
            {
                Status = "检查更新...";
                var hasUpdate = await _versionCheckerService.CheckForUpdatesAsync();
                if (hasUpdate)
                {
                    _loggerService.LogInfo("发现新版本可用");
                }
                else
                {
                    _loggerService.LogInfo("当前版本是最新的");
                }
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"检查更新失败: {ex.Message}");
            }
            finally
            {
                Status = "就绪";
            }
        }

        private async void StartExecution()
        {
            try
            {
                Status = "执行中...";
                IsProgressVisible = true;
                Progress = 0;

                _loggerService.LogInfo("开始执行任务");

                // Simulate execution progress
                for (int i = 0; i <= 100; i += 10)
                {
                    Progress = i;
                    await Task.Delay(200);
                    
                    if (i == 30)
                        _loggerService.LogInfo("正在初始化系统组件...");
                    else if (i == 60)
                        _loggerService.LogInfo("正在执行核心任务...");
                    else if (i == 90)
                        _loggerService.LogInfo("正在完成最后步骤...");
                }

                await _executorService.ExecuteAsync();
                
                _loggerService.LogSuccess("任务执行完成");
                Status = "完成";
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"执行失败: {ex.Message}");
                Status = "错误";
            }
            finally
            {
                IsProgressVisible = false;
                Progress = 0;
            }
        }

        private void StopExecution()
        {
            _executorService.Stop();
            _loggerService.LogWarning("执行已停止");
            Status = "已停止";
            IsProgressVisible = false;
            Progress = 0;
        }

        private bool CanStartExecution()
        {
            return !_executorService.IsRunning;
        }

        private bool CanStopExecution()
        {
            return _executorService.IsRunning;
        }

        private void ClearLog()
        {
            LogEntries.Clear();
            _loggerService.LogInfo("日志已清除");
        }

        private void SaveLog()
        {
            try
            {
                var fileName = $"AugmentMagic_Log_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                var content = string.Join(Environment.NewLine, 
                    LogEntries.Select(e => $"[{e.Timestamp:HH:mm:ss}] [{e.Level}] {e.Message}"));
                
                File.WriteAllText(fileName, content);
                _loggerService.LogSuccess($"日志已保存到: {fileName}");
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"保存日志失败: {ex.Message}");
            }
        }

        private void OpenSettings()
        {
            _loggerService.LogInfo("打开设置窗口");
            // TODO: Implement settings window
        }

        private void ShowAbout()
        {
            var aboutMessage = $@"Augment Magic v1.0.0

系统信息:
- 操作系统: {SystemInfo.SystemName}
- 架构: {SystemInfo.Architecture}
- 管理员权限: {SystemInfo.IsAdminText}

这是一个现代化的系统工具，具有科技风格的界面设计。

© 2024 Augment Code. All rights reserved.";

            System.Windows.MessageBox.Show(aboutMessage, "关于 Augment Magic", 
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        private void OnLogEntryAdded(object? sender, LogEntry logEntry)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                LogEntries.Add(logEntry);
                
                // Keep only last 1000 entries
                while (LogEntries.Count > 1000)
                {
                    LogEntries.RemoveAt(0);
                }
            });
        }

        private void StartTimeUpdateTimer()
        {
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now.ToString("HH:mm:ss");
            timer.Start();
        }
    }
}
