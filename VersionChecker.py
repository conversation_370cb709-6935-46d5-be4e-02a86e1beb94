#!/usr/bin/env python3
"""
VersionChecker - 版本检查器
从 .pyc 文件分析重构而来
"""

import requests
import json
import os
from datetime import datetime
from packaging import version


class VersionChecker:
    """版本检查器类"""
    
    def __init__(self, current_version="1.0.0"):
        self.current_version = current_version
        self.update_url = "https://api.github.com/repos/augment-code/augment-magic/releases/latest"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AugmentMagic/1.0.0'
        })
    
    def get_current_version(self):
        """获取当前版本"""
        return self.current_version
    
    def check_for_updates(self):
        """检查是否有更新"""
        try:
            response = self.session.get(self.update_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                latest_version = data.get('tag_name', '').lstrip('v')
                
                if latest_version:
                    return self.compare_versions(self.current_version, latest_version)
            
        except Exception as e:
            print(f"检查更新失败: {e}")
        
        return False
    
    def compare_versions(self, current, latest):
        """比较版本号"""
        try:
            return version.parse(latest) > version.parse(current)
        except Exception:
            # 如果版本解析失败，使用简单的字符串比较
            return latest != current
    
    def get_latest_version_info(self):
        """获取最新版本信息"""
        try:
            response = self.session.get(self.update_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return {
                    'version': data.get('tag_name', '').lstrip('v'),
                    'name': data.get('name', ''),
                    'body': data.get('body', ''),
                    'published_at': data.get('published_at', ''),
                    'download_url': data.get('html_url', ''),
                    'assets': data.get('assets', [])
                }
        except Exception as e:
            print(f"获取版本信息失败: {e}")
        
        return None
    
    def download_update(self, download_url, save_path):
        """下载更新文件"""
        try:
            response = self.session.get(download_url, stream=True, timeout=30)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                return True
        except Exception as e:
            print(f"下载更新失败: {e}")
        
        return False
    
    def get_update_assets(self):
        """获取更新资源列表"""
        version_info = self.get_latest_version_info()
        if version_info and version_info['assets']:
            return version_info['assets']
        return []
    
    def find_suitable_asset(self, assets):
        """查找适合当前系统的资源"""
        import platform
        
        system = platform.system().lower()
        arch = platform.machine().lower()
        
        # 系统映射
        system_map = {
            'windows': 'windows',
            'darwin': 'macos',
            'linux': 'linux'
        }
        
        # 架构映射
        arch_map = {
            'x86_64': 'x86_64',
            'amd64': 'x86_64',
            'arm64': 'aarch64',
            'aarch64': 'aarch64'
        }
        
        target_system = system_map.get(system, system)
        target_arch = arch_map.get(arch, arch)
        
        # 查找匹配的资源
        for asset in assets:
            name = asset.get('name', '').lower()
            if target_system in name and target_arch in name:
                return asset
        
        # 如果没有找到精确匹配，返回第一个
        return assets[0] if assets else None
    
    def check_and_notify_update(self):
        """检查更新并返回通知信息"""
        if self.check_for_updates():
            version_info = self.get_latest_version_info()
            if version_info:
                return {
                    'has_update': True,
                    'current_version': self.current_version,
                    'latest_version': version_info['version'],
                    'release_notes': version_info['body'],
                    'download_url': version_info['download_url']
                }
        
        return {
            'has_update': False,
            'current_version': self.current_version,
            'latest_version': self.current_version
        }
    
    def save_version_info(self, file_path="version_info.json"):
        """保存版本信息到文件"""
        version_info = self.get_latest_version_info()
        if version_info:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(version_info, f, indent=2, ensure_ascii=False)
                return True
            except Exception as e:
                print(f"保存版本信息失败: {e}")
        
        return False
    
    def load_version_info(self, file_path="version_info.json"):
        """从文件加载版本信息"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载版本信息失败: {e}")
        
        return None


# 测试代码
if __name__ == "__main__":
    checker = VersionChecker("1.0.0")
    
    print(f"当前版本: {checker.get_current_version()}")
    
    # 检查更新
    has_update = checker.check_for_updates()
    print(f"有更新: {has_update}")
    
    if has_update:
        version_info = checker.get_latest_version_info()
        if version_info:
            print(f"最新版本: {version_info['version']}")
            print(f"发布说明: {version_info['body'][:100]}...")
    
    # 获取更新通知
    notification = checker.check_and_notify_update()
    print(f"更新通知: {notification}")
