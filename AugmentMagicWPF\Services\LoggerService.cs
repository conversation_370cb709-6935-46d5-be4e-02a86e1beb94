using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public class LoggerService : ILoggerService
    {
        private readonly ConcurrentQueue<LogEntry> _logEntries = new();
        private readonly object _lock = new();

        public event EventHandler<LogEntry>? LogEntryAdded;

        public void LogDebug(string message)
        {
            AddLogEntry(LogLevel.Debug, message);
        }

        public void LogInfo(string message)
        {
            AddLogEntry(LogLevel.Info, message);
        }

        public void LogWarning(string message)
        {
            AddLogEntry(LogLevel.Warning, message);
        }

        public void LogError(string message)
        {
            AddLogEntry(LogLevel.Error, message);
        }

        public void LogSuccess(string message)
        {
            AddLogEntry(LogLevel.Success, message);
        }

        public IEnumerable<LogEntry> GetLogEntries()
        {
            return _logEntries.ToArray();
        }

        public void ClearLog()
        {
            lock (_lock)
            {
                while (_logEntries.TryDequeue(out _)) { }
            }
        }

        public async Task SaveLogToFileAsync(string filePath)
        {
            try
            {
                var entries = GetLogEntries();
                var lines = entries.Select(e => $"[{e.Timestamp:yyyy-MM-dd HH:mm:ss}] [{e.Level}] {e.Message}");
                await File.WriteAllLinesAsync(filePath, lines);
            }
            catch (Exception ex)
            {
                // Can't log this error through the same service, so we'll ignore it
                System.Diagnostics.Debug.WriteLine($"Failed to save log: {ex.Message}");
            }
        }

        private void AddLogEntry(LogLevel level, string message)
        {
            var entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message
            };

            lock (_lock)
            {
                _logEntries.Enqueue(entry);
                
                // Keep only last 1000 entries
                while (_logEntries.Count > 1000)
                {
                    _logEntries.TryDequeue(out _);
                }
            }

            LogEntryAdded?.Invoke(this, entry);
        }
    }
}
