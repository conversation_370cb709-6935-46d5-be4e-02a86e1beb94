"""
pyimod01_archive - 从 .pyc 文件重构
"""

# 可能的导入:
# _parse_offset_from_filename
# Parse the numeric offset from filename, stored as: `/path/to/file?offset`.
# -ZlibArchiveReader._parse_offset_from_filenameO
# Extract data from entry with the given name.
#  appears to have been moved or deleted since this application was launched. Continouation from this state is impossible. Exiting now.z
# ImportError)
# _frozen_importlib

# 可能的类:
class ZlibArchiveReader:
    """重构的类"""
    pass

class FileNotFoundError:
    """重构的类"""
    pass

class ArchiveReadError:
    """重构的类"""
    pass

class RuntimeErrorr:
    """重构的类"""
    pass

class SystemExit:
    """重构的类"""
    pass

class EOFError:
    """重构的类"""
    pass

# 可能的函数:
def PYTHON_MAGIC_NUMBER():
    """重构的函数"""
    pass

def entry_offset():
    """重构的函数"""
    pass

def SEEK_SET():
    """重构的函数"""
    pass

def typecode():
    """重构的函数"""
    pass

def toc_offset():
    """重构的函数"""
    pass

def __qualname__():
    """重构的函数"""
    pass

def magic():
    """重构的函数"""
    pass

def pymagic():
    """重构的函数"""
    pass

def _start_offset():
    """重构的函数"""
    pass

def PYZ_ITEM_DATArE():
    """重构的函数"""
    pass

def read():
    """重构的函数"""
    pass

def unpack():
    """重构的函数"""
    pass

def zlib():
    """重构的函数"""
    pass

def check_pymagic():
    """重构的函数"""
    pass

def filename():
    """重构的函数"""
    pass

def __module__():
    """重构的函数"""
    pass

def loads():
    """重构的函数"""
    pass

def struct():
    """重构的函数"""
    pass

def name():
    """重构的函数"""
    pass

def extract():
    """重构的函数"""
    pass

def marshal():
    """重构的函数"""
    pass

def dict():
    """重构的函数"""
    pass

def rfind():
    """重构的函数"""
    pass

def __static_attributes__():
    """重构的函数"""
    pass

def __firstlineno__():
    """重构的函数"""
    pass

def decompress():
    """重构的函数"""
    pass

def idxs():
    """重构的函数"""
    pass

def PYZ_ITEM_PKG():
    """重构的函数"""
    pass

def start_offset():
    """重构的函数"""
    pass

def _PYZ_MAGIC_PATTERN():
    """重构的函数"""
    pass

def __name__():
    """重构的函数"""
    pass

def staticmethod():
    """重构的函数"""
    pass

def get():
    """重构的函数"""
    pass

def _parse_offset_from_filename():
    """重构的函数"""
    pass

def _frozen_importlib():
    """重构的函数"""
    pass

def open():
    """重构的函数"""
    pass

def PYZ_ITEM_MODULE():
    """重构的函数"""
    pass

def seek():
    """重构的函数"""
    pass

def __init__():
    """重构的函数"""
    pass

def offset():
    """重构的函数"""
    pass

def _bootstrap_external():
    """重构的函数"""
    pass

def _filename():
    """重构的函数"""
    pass

def entry():
    """重构的函数"""
    pass

# 从 .pyc 文件中提取的所有字符串:
#   1: 'ArchiveReadError'
#   2: '__name__'
#   3: '__module__'
#   4: '__qualname__'
#   5: '__firstlineno__'
#   6: '__static_attributes__r'
#   7: '&PyInstaller\\loader\\pyimod01_archive.pyr'
#   8: 'ZlibArchiveReader'
#  10: 'modules, as individually-compressed entries.'
#  11: 'PYZ'
#  12: 'X l'
#  13: 'XPR'
#  14: 'pxU'
#  15: 'rbz'
#  16: 'PYZ magic pattern mismatch!z'
#  17: 'Python magic pattern mismatch!z'
#  18: '_filename'
#  19: '_start_offset'
#  20: 'toc'
#  21: '_parse_offset_from_filename'
#  22: 'open'
#  23: 'seek'
#  24: 'SEEK_SET'
#  25: 'read'
#  26: 'len'
#  27: '_PYZ_MAGIC_PATTERNr'
#  28: 'PYTHON_MAGIC_NUMBER'
#  29: 'struct'
#  30: 'unpack'
#  31: 'dict'
#  32: 'marshal'
#  33: 'load)'
#  34: 'self'
#  35: 'filename'
#  36: 'start_offset'
#  37: 'check_pymagic'
#  38: 'magic'
#  39: 'pymagic'
#  40: 'toc_offset'
#  41: '         r'
#  42: '__init__'
#  43: 'ZlibArchiveReader.__init__.'
#  44: 'Parse the numeric offset from filename, stored as: `/path/to/file?offset`.'
#  45: 'rfind'
#  46: 'int'
#  47: 'ValueError)'
#  48: 'offset'
#  49: 'idxs'
#  50: '   r'
#  51: '-ZlibArchiveReader._parse_offset_from_filenameO'
#  52: 'pEn'
#  53: 'NY='
#  54: 'Extract data from entry with the given name.'
#  56: 'raw data, set `raw` flag to True.'
#  57: 'ERROR: z'
#  59: 'Failed to unmarshal PYZ entry '
#  60: 'getr'
#  61: 'FileNotFoundError'
#  62: 'SystemExit'
#  63: 'zlib'
#  64: 'decompress'
#  65: 'PYZ_ITEM_MODULE'
#  66: 'PYZ_ITEM_PKG'
#  67: 'PYZ_ITEM_NSPKGr&'
#  68: 'loads'
#  69: 'EOFError'
#  70: 'ImportError)'
#  71: 'name'
#  72: 'raw'
#  73: 'entry'
#  74: 'typecode'
#  75: 'entry_offset'
#  76: 'entry_lengthr,'
#  77: 'obj'
#  78: '          r'
#  79: 'extract'
#  80: 'ZlibArchiveReader.extractc'
#  81: '0B?'
#  82: '$C4'
#  83: 'NF)'
#  84: '__doc__r!'
#  85: 'staticmethodr'
#  86: '_frozen_importlib'
#  87: '_bootstrap_external'
#  88: 'MAGIC_NUMBERr"'
#  89: 'PYZ_ITEM_DATArE'
#  90: 'RuntimeErrorr'
#  91: '<module>rZ'