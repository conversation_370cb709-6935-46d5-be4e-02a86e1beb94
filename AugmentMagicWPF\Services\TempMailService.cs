using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public class TempMailService : ITempMailService
    {
        private readonly HttpClient _httpClient;
        private readonly string[] _domains = {
            "tempmail.org",
            "10minutemail.com",
            "guerrillamail.com",
            "mailinator.com",
            "temp-mail.org"
        };

        public TempMailService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "AugmentMagic/1.0.0");
        }

        public async Task<string> GetNewEmailAsync()
        {
            try
            {
                // Try to get email from API first
                var apiEmail = await TryGetEmailFromApi();
                if (!string.IsNullOrEmpty(apiEmail))
                {
                    return apiEmail;
                }

                // Fallback to generating random email
                return GenerateRandomEmail();
            }
            catch
            {
                return GenerateRandomEmail();
            }
        }

        public async Task<IEnumerable<EmailMessage>> CheckInboxAsync(string email)
        {
            try
            {
                // Try to check inbox via API
                var apiEmails = await TryCheckInboxFromApi(email);
                if (apiEmails.Any())
                {
                    return apiEmails;
                }

                // Return empty list if no API available
                return new List<EmailMessage>();
            }
            catch
            {
                return new List<EmailMessage>();
            }
        }

        public async Task<EmailMessage?> GetEmailContentAsync(string emailId)
        {
            try
            {
                // This would typically call an API to get email content
                // For now, return null as we don't have a real API
                await Task.Delay(100); // Simulate API call
                return null;
            }
            catch
            {
                return null;
            }
        }

        public string ExtractVerificationCode(string content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            // Common verification code patterns
            var patterns = new[]
            {
                @"验证码[：:]\s*(\d{4,8})",
                @"verification code[：:]\s*(\d{4,8})",
                @"code[：:]\s*(\d{4,8})",
                @"\b(\d{6})\b",  // 6-digit codes
                @"\b(\d{4})\b"   // 4-digit codes
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return string.Empty;
        }

        public async Task<bool> WaitForEmailAsync(string email, TimeSpan timeout)
        {
            var startTime = DateTime.Now;
            var checkInterval = TimeSpan.FromSeconds(10);

            while (DateTime.Now - startTime < timeout)
            {
                var emails = await CheckInboxAsync(email);
                if (emails.Any())
                {
                    return true;
                }

                await Task.Delay(checkInterval);
            }

            return false;
        }

        private async Task<string> TryGetEmailFromApi()
        {
            try
            {
                // This is a placeholder for actual API integration
                // You would implement actual API calls here
                await Task.Delay(500); // Simulate API call
                
                // For demo purposes, return a generated email
                return GenerateRandomEmail();
            }
            catch
            {
                return string.Empty;
            }
        }

        private async Task<IEnumerable<EmailMessage>> TryCheckInboxFromApi(string email)
        {
            try
            {
                // This is a placeholder for actual API integration
                await Task.Delay(500); // Simulate API call
                
                // For demo purposes, sometimes return a fake email
                if (Random.Shared.Next(0, 10) < 3) // 30% chance
                {
                    return new List<EmailMessage>
                    {
                        new EmailMessage
                        {
                            Id = Guid.NewGuid().ToString(),
                            From = "<EMAIL>",
                            To = email,
                            Subject = "验证码",
                            Body = "您的验证码是: 123456",
                            ReceivedAt = DateTime.Now,
                            IsRead = false
                        }
                    };
                }

                return new List<EmailMessage>();
            }
            catch
            {
                return new List<EmailMessage>();
            }
        }

        private string GenerateRandomEmail()
        {
            var random = new Random();
            var username = GenerateRandomString(8);
            var domain = _domains[random.Next(_domains.Length)];
            return $"{username}@{domain}";
        }

        private string GenerateRandomString(int length)
        {
            const string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
