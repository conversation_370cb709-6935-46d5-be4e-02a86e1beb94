using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AugmentMagicWPF.Models;

namespace AugmentMagicWPF.Services
{
    public class TempMailService : ITempMailService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "https://api.tempmail.org";
        private readonly string[] _domains = {
            "tempmail.org",
            "10minutemail.com",
            "guerrillamail.com",
            "mailinator.com",
            "temp-mail.org"
        };

        public TempMailService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "AugmentMagic/1.0.0");
        }

        public async Task<string> GetNewEmailAsync()
        {
            try
            {
                // Try to get email from API first
                var apiEmail = await TryGetEmailFromApi();
                if (!string.IsNullOrEmpty(apiEmail))
                {
                    System.Diagnostics.Debug.WriteLine($"从API获取邮箱: {apiEmail}");
                    return apiEmail;
                }

                // Fallback to generating random email
                var randomEmail = GenerateRandomEmail();
                System.Diagnostics.Debug.WriteLine($"生成随机邮箱: {randomEmail}");
                return randomEmail;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取邮箱异常: {ex.Message}");
                var fallbackEmail = GenerateRandomEmail();
                System.Diagnostics.Debug.WriteLine($"异常后生成邮箱: {fallbackEmail}");
                return fallbackEmail;
            }
        }

        public async Task<IEnumerable<EmailMessage>> CheckInboxAsync(string email)
        {
            try
            {
                // Try to check inbox via API
                var apiEmails = await TryCheckInboxFromApi(email);
                if (apiEmails.Any())
                {
                    return apiEmails;
                }

                // Return empty list if no API available
                return new List<EmailMessage>();
            }
            catch
            {
                return new List<EmailMessage>();
            }
        }

        public async Task<EmailMessage?> GetEmailContentAsync(string emailId)
        {
            try
            {
                // This would typically call an API to get email content
                // For now, return null as we don't have a real API
                await Task.Delay(100); // Simulate API call
                return null;
            }
            catch
            {
                return null;
            }
        }

        public string ExtractVerificationCode(string content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            // Common verification code patterns
            var patterns = new[]
            {
                @"验证码[：:]\s*(\d{4,8})",
                @"verification code[：:]\s*(\d{4,8})",
                @"code[：:]\s*(\d{4,8})",
                @"\b(\d{6})\b",  // 6-digit codes
                @"\b(\d{4})\b"   // 4-digit codes
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(content, pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return string.Empty;
        }

        public async Task<bool> WaitForEmailAsync(string email, TimeSpan timeout)
        {
            var startTime = DateTime.Now;
            var checkInterval = TimeSpan.FromSeconds(10);

            while (DateTime.Now - startTime < timeout)
            {
                var emails = await CheckInboxAsync(email);
                if (emails.Any())
                {
                    return true;
                }

                await Task.Delay(checkInterval);
            }

            return false;
        }

        private async Task<string> TryGetEmailFromApi()
        {
            try
            {
                // 尝试从真实API获取邮箱
                var response = await _httpClient.GetAsync($"{_baseUrl}/request/mail/id/");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<JsonElement>(content);

                    if (data.TryGetProperty("mail", out var mailProperty))
                    {
                        return mailProperty.GetString() ?? string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {
                // API调用失败，记录错误但不抛出异常
                System.Diagnostics.Debug.WriteLine($"API获取邮箱失败: {ex.Message}");
            }

            // 如果API失败，返回空字符串，让调用方使用随机邮箱
            return string.Empty;
        }

        private async Task<IEnumerable<EmailMessage>> TryCheckInboxFromApi(string email)
        {
            try
            {
                // 尝试从真实API检查收件箱
                var response = await _httpClient.GetAsync($"{_baseUrl}/request/mail/id/{email}/");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<JsonElement>(content);

                    if (data.TryGetProperty("list", out var listProperty) && listProperty.ValueKind == JsonValueKind.Array)
                    {
                        var emails = new List<EmailMessage>();
                        foreach (var emailElement in listProperty.EnumerateArray())
                        {
                            var emailMessage = new EmailMessage
                            {
                                Id = emailElement.TryGetProperty("id", out var idProp) ? idProp.GetString() ?? Guid.NewGuid().ToString() : Guid.NewGuid().ToString(),
                                From = emailElement.TryGetProperty("from", out var fromProp) ? fromProp.GetString() ?? "unknown" : "unknown",
                                To = email,
                                Subject = emailElement.TryGetProperty("subject", out var subjectProp) ? subjectProp.GetString() ?? "No Subject" : "No Subject",
                                Body = emailElement.TryGetProperty("body", out var bodyProp) ? bodyProp.GetString() ?? "" : "",
                                ReceivedAt = emailElement.TryGetProperty("date", out var dateProp) ?
                                    DateTime.TryParse(dateProp.GetString(), out var parsedDate) ? parsedDate : DateTime.Now : DateTime.Now,
                                IsRead = false
                            };
                            emails.Add(emailMessage);
                        }
                        return emails;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"API检查收件箱失败: {ex.Message}");
            }

            // 如果API失败，为了演示目的，有时返回一个模拟邮件
            if (Random.Shared.Next(0, 10) < 2) // 20% chance for demo
            {
                var random = new Random();
                var verificationCode = random.Next(100000, 999999).ToString(); // 生成6位随机验证码

                return new List<EmailMessage>
                {
                    new EmailMessage
                    {
                        Id = Guid.NewGuid().ToString(),
                        From = "<EMAIL>",
                        To = email,
                        Subject = "验证码",
                        Body = $"您的验证码是: {verificationCode}",
                        ReceivedAt = DateTime.Now,
                        IsRead = false
                    }
                };
            }

            return new List<EmailMessage>();
        }

        private string GenerateRandomEmail()
        {
            try
            {
                var random = new Random();
                var username = GenerateRandomString(8);
                var domain = _domains[random.Next(_domains.Length)];
                var email = $"{username}@{domain}";
                System.Diagnostics.Debug.WriteLine($"生成的邮箱: {email}");
                return email;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成邮箱失败: {ex.Message}");
                // 如果生成失败，返回一个简单的固定邮箱
                return $"test{DateTime.Now.Ticks % 100000}@tempmail.org";
            }
        }

        private string GenerateRandomString(int length)
        {
            try
            {
                const string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
                var random = new Random();
                return new string(Enumerable.Repeat(chars, length)
                    .Select(s => s[random.Next(s.Length)]).ToArray());
            }
            catch
            {
                // 如果生成失败，返回简单字符串
                return $"user{DateTime.Now.Ticks % 10000}";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
