using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace AugmentMagicWPF.Services
{
    public class ExecutorService : IExecutorService
    {
        private readonly ISystemInfoService _systemInfoService;
        private readonly ILoggerService _loggerService;
        private Process? _currentProcess;
        private CancellationTokenSource? _cancellationTokenSource;

        public ExecutorService(ISystemInfoService systemInfoService, ILoggerService loggerService)
        {
            _systemInfoService = systemInfoService;
            _loggerService = loggerService;
        }

        public bool IsRunning => _currentProcess != null && !_currentProcess.HasExited;

        public event EventHandler<string>? OutputReceived;
        public event EventHandler<string>? ErrorReceived;
        public event EventHandler? ExecutionCompleted;

        public async Task ExecuteAsync()
        {
            if (IsRunning)
            {
                throw new InvalidOperationException("Execution is already running");
            }

            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                
                // Get the executable path
                var executableFilename = _systemInfoService.GetExecutableFilename();
                var executablePath = _systemInfoService.GetResourcePath($"data/{executableFilename}");

                if (!File.Exists(executablePath))
                {
                    _loggerService.LogError($"可执行文件不存在: {executablePath}");
                    
                    // Simulate execution for demo purposes
                    await SimulateExecution(_cancellationTokenSource.Token);
                    return;
                }

                _loggerService.LogInfo($"开始执行: {executablePath}");

                var startInfo = new ProcessStartInfo
                {
                    FileName = executablePath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                _currentProcess = new Process { StartInfo = startInfo };
                
                _currentProcess.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        OutputReceived?.Invoke(this, e.Data);
                        _loggerService.LogInfo($"输出: {e.Data}");
                    }
                };

                _currentProcess.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        ErrorReceived?.Invoke(this, e.Data);
                        _loggerService.LogError($"错误: {e.Data}");
                    }
                };

                _currentProcess.Start();
                _currentProcess.BeginOutputReadLine();
                _currentProcess.BeginErrorReadLine();

                await _currentProcess.WaitForExitAsync(_cancellationTokenSource.Token);
                
                _loggerService.LogInfo($"执行完成，退出代码: {_currentProcess.ExitCode}");
            }
            catch (OperationCanceledException)
            {
                _loggerService.LogWarning("执行被取消");
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"执行失败: {ex.Message}");
            }
            finally
            {
                _currentProcess?.Dispose();
                _currentProcess = null;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                
                ExecutionCompleted?.Invoke(this, EventArgs.Empty);
            }
        }

        public void Stop()
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                
                if (_currentProcess != null && !_currentProcess.HasExited)
                {
                    _currentProcess.Kill(true);
                    _loggerService.LogWarning("进程已被强制终止");
                }
            }
            catch (Exception ex)
            {
                _loggerService.LogError($"停止执行失败: {ex.Message}");
            }
        }

        private async Task SimulateExecution(CancellationToken cancellationToken)
        {
            _loggerService.LogInfo("模拟执行开始（未找到可执行文件）");
            
            var tasks = new[]
            {
                "初始化系统组件",
                "检查系统权限",
                "加载配置文件",
                "连接网络服务",
                "验证用户身份",
                "执行核心任务",
                "生成报告",
                "清理临时文件",
                "保存结果",
                "完成执行"
            };

            for (int i = 0; i < tasks.Length; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var task = tasks[i];
                _loggerService.LogInfo($"[{i + 1}/{tasks.Length}] {task}...");
                OutputReceived?.Invoke(this, $"执行步骤 {i + 1}: {task}");
                
                // Simulate some work
                await Task.Delay(Random.Shared.Next(500, 1500), cancellationToken);
                
                if (Random.Shared.Next(0, 10) < 2) // 20% chance of warning
                {
                    _loggerService.LogWarning($"{task} 完成，但有警告");
                }
                else
                {
                    _loggerService.LogSuccess($"{task} 完成");
                }
            }

            if (!cancellationToken.IsCancellationRequested)
            {
                _loggerService.LogSuccess("模拟执行完成");
            }
        }

        public void Dispose()
        {
            Stop();
            _currentProcess?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}
